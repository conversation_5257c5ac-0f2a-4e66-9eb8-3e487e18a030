# 🌟 Hello World MCP Server

Your first **real** Model Context Protocol server! This demonstrates the simplest possible MCP server using the official Python SDK.

## 🎯 What We're Building

A proper MCP server that:
- Uses the official MCP Python SDK
- Exposes a greeting tool to LLMs
- Communicates via JSON-RPC over stdio
- Works with <PERSON> and other MCP clients

## 📚 Key Concepts

### MCP Server vs Regular Function

**Regular Function:**
```python
def greet(name):
    return f"Hello {name}!"

# Direct call
result = greet("Alice")
```

**MCP Server:**
```python
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("hello")

@mcp.tool()
async def greet(name: str) -> str:
    """Greet someone by name"""
    return f"Hello {name}!"

# LL<PERSON> discovers and calls automatically via MCP protocol
```

## 🔧 How MCP Works

1. **Server Startup**: MCP server starts and listens on stdio
2. **Client Connection**: MCP client (like <PERSON>) connects via JSON-RPC
3. **Tool Discovery**: <PERSON><PERSON> asks server what tools are available
4. **Tool Execution**: LL<PERSON> decides to use a tool, client calls server
5. **Response**: Server executes tool and returns result to LLM

## 🚀 Running the Example

### 1. Install Dependencies
```bash
pip install mcp
```

### 2. Test the Server Directly
```bash
python hello_server.py
```

### 3. Test with MCP Client
```bash
python test_client.py
```

### 4. Connect to Claude Desktop
Add to your `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "hello": {
      "command": "python",
      "args": ["/absolute/path/to/hello_server.py"]
    }
  }
}
```

## 🔍 What Happens Behind the Scenes

### JSON-RPC Communication
```json
// Client asks for available tools
{"jsonrpc": "2.0", "method": "tools/list", "id": 1}

// Server responds with tool definitions
{
  "jsonrpc": "2.0", 
  "id": 1,
  "result": {
    "tools": [{
      "name": "greet",
      "description": "Greet someone by name",
      "inputSchema": {
        "type": "object",
        "properties": {
          "name": {"type": "string"}
        },
        "required": ["name"]
      }
    }]
  }
}

// Client calls tool
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "greet",
    "arguments": {"name": "Alice"}
  },
  "id": 2
}

// Server returns result
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "content": [{"type": "text", "text": "Hello Alice!"}]
  }
}
```

## 🎓 Learning Objectives

After this example, you'll understand:
- ✅ How to create an MCP server with the official SDK
- ✅ How to define tools that LLMs can discover and use
- ✅ How MCP communication works (JSON-RPC)
- ✅ How to test MCP servers
- ✅ How to connect servers to Claude Desktop

## 🔧 Try These Modifications

### 1. Add Parameters
```python
@mcp.tool()
async def greet(name: str, language: str = "english") -> str:
    """Greet someone in different languages"""
    greetings = {
        "english": f"Hello {name}!",
        "spanish": f"¡Hola {name}!",
        "french": f"Bonjour {name}!",
    }
    return greetings.get(language, f"Hello {name}!")
```

### 2. Add Input Validation
```python
@mcp.tool()
async def greet(name: str) -> str:
    """Greet someone by name"""
    if not name or not name.strip():
        raise ValueError("Name cannot be empty")
    
    if len(name) > 50:
        raise ValueError("Name too long")
    
    return f"Hello {name.strip()}!"
```

### 3. Add Multiple Tools
```python
@mcp.tool()
async def greet(name: str) -> str:
    """Greet someone by name"""
    return f"Hello {name}!"

@mcp.tool()
async def farewell(name: str) -> str:
    """Say goodbye to someone"""
    return f"Goodbye {name}! See you later!"
```

## 🚀 Next Steps

1. **Experiment**: Modify the greeting tool
2. **Test**: Try different inputs and see the responses
3. **Connect**: Add to Claude Desktop and test with natural language
4. **Advance**: Move to `02_calculator/` for more complex examples

---

**This is real MCP! 🎉 Your server can now work with any MCP client!**
