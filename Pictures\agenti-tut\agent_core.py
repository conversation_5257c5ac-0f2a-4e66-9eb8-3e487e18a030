"""
Core Agent Framework - The Foundation of Our AI Agent
This file contains the main agent class and tool management system.
"""

import os
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class Tool:
    """Represents a tool that the agent can use"""
    name: str
    description: str
    parameters: Dict[str, Any]
    function: callable
    
    def to_openai_format(self) -> Dict[str, Any]:
        """Convert tool to OpenAI function calling format"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }

class MistralAgent:
    """
    A powerful AI agent that can use tools to accomplish tasks.
    
    This agent can:
    - Chat with users naturally
    - Use tools to interact with external systems
    - Remember conversation context
    - Plan and execute multi-step tasks
    """
    
    def __init__(self, name: str = "Assistant", system_prompt: str = None):
        """Initialize the agent with Mistral AI"""
        self.name = name
        self.client = OpenAI(
            api_key=os.getenv("MISTRAL_API_KEY"),
            base_url="https://codestral.mistral.ai/v1"
        )
        self.model = "codestral-latest"
        self.tools = {}  # Dictionary to store tools by name
        self.conversation_history = []
        
        # Set default system prompt
        default_prompt = f"""You are {name}, a helpful AI assistant with access to various tools.
        
Key behaviors:
- Always be helpful, friendly, and professional
- Use tools when they can help accomplish the user's request
- Explain what you're doing when using tools
- If you can't help with something, explain why clearly
- Be concise but thorough in your responses"""
        
        self.system_prompt = system_prompt or default_prompt
        
        # Add system message to conversation history
        self.conversation_history.append({
            "role": "system",
            "content": self.system_prompt
        })
    
    def add_tool(self, tool: Tool):
        """Add a tool to the agent's toolkit"""
        self.tools[tool.name] = tool
        print(f"🔧 Added tool: {tool.name}")
    
    def list_tools(self) -> List[str]:
        """Get list of available tool names"""
        return list(self.tools.keys())
    
    def chat(self, message: str) -> str:
        """
        Main chat interface - send a message and get a response
        
        Args:
            message: User's message
            
        Returns:
            Agent's response after potentially using tools
        """
        print(f"👤 User: {message}")
        
        # Add user message to history
        self.conversation_history.append({
            "role": "user", 
            "content": message
        })
        
        # Prepare tools for API call
        tools_schema = [tool.to_openai_format() for tool in self.tools.values()]
        
        try:
            # Call LLM with tools
            response = self.client.chat.completions.create(
                model=self.model,
                messages=self.conversation_history,
                tools=tools_schema if tools_schema else None,
                tool_choice="auto" if tools_schema else None,
                temperature=0.7
            )
            
            return self._process_response(response)
            
        except Exception as e:
            error_msg = f"Sorry, I encountered an error: {str(e)}"
            print(f"❌ Error: {error_msg}")
            return error_msg
    
    def _process_response(self, response) -> str:
        """Process LLM response and execute any tool calls"""
        message = response.choices[0].message
        
        # Add assistant message to history
        self.conversation_history.append({
            "role": "assistant",
            "content": message.content,
            "tool_calls": message.tool_calls
        })
        
        # Execute tool calls if any
        if message.tool_calls:
            print(f"🤖 {self.name}: I'll use some tools to help you...")
            
            for tool_call in message.tool_calls:
                result = self._execute_tool(tool_call)
                
                # Add tool result to conversation
                self.conversation_history.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": result
                })
            
            # Get final response after tool execution
            final_response = self.client.chat.completions.create(
                model=self.model,
                messages=self.conversation_history,
                temperature=0.7
            )
            
            final_message = final_response.choices[0].message.content
            
            # Add final response to history
            self.conversation_history.append({
                "role": "assistant",
                "content": final_message
            })
            
            print(f"🤖 {self.name}: {final_message}")
            return final_message
        
        print(f"🤖 {self.name}: {message.content}")
        return message.content or "I'm not sure how to respond to that."
    
    def _execute_tool(self, tool_call) -> str:
        """Execute a specific tool call"""
        tool_name = tool_call.function.name
        
        try:
            tool_args = json.loads(tool_call.function.arguments)
        except json.JSONDecodeError:
            return f"Error: Invalid arguments for {tool_name}"
        
        print(f"🔧 Using tool: {tool_name} with args: {tool_args}")
        
        # Find the tool
        tool = self.tools.get(tool_name)
        if not tool:
            return f"Error: Tool {tool_name} not found"
        
        try:
            # Execute the tool function
            result = tool.function(**tool_args)
            print(f"✅ Tool result: {result}")
            return str(result)
        except Exception as e:
            error_msg = f"Error executing {tool_name}: {str(e)}"
            print(f"❌ {error_msg}")
            return error_msg
    
    def clear_history(self):
        """Clear conversation history but keep system prompt"""
        self.conversation_history = [self.conversation_history[0]]  # Keep system prompt
        print("🧹 Conversation history cleared")
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the conversation"""
        user_messages = [msg for msg in self.conversation_history if msg["role"] == "user"]
        tool_calls = sum(1 for msg in self.conversation_history if msg.get("tool_calls"))
        
        return f"""
📊 Conversation Summary:
- Messages exchanged: {len(user_messages)}
- Tools used: {tool_calls}
- Available tools: {len(self.tools)}
- Tools: {', '.join(self.list_tools()) if self.tools else 'None'}
"""

# Example usage and testing
if __name__ == "__main__":
    # Create a simple agent
    agent = MistralAgent("TestBot")
    
    print("🚀 Agent created! Testing basic functionality...")
    print(agent.get_conversation_summary())
    
    # Test basic chat without tools
    response = agent.chat("Hello! What can you do?")
    
    print("\n" + "="*50)
    print("✅ Basic agent test completed!")
    print("Next: Add some tools to make it more powerful!")
