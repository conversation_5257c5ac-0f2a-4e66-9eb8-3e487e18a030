#!/usr/bin/env python3
"""
🧪 Grid Bot Adventure MCP Client Test

This script tests the grid game MCP server by simulating
how an LLM would strategically play the game.

It demonstrates:
- Strategic game analysis
- Pathfinding and threat assessment
- Multi-step planning
- Adaptive gameplay
"""

import asyncio
import json
import subprocess
import sys
import time
from typing import Any, Dict

class GridGameMCPTestClient:
    """Test client for the Grid Bot Adventure MCP server"""
    
    def __init__(self, server_command: list[str]):
        self.server_command = server_command
        self.process = None
        self.request_id = 0
    
    async def start_server(self):
        """Start the MCP server process"""
        print(f"🚀 Starting Grid Bot Adventure MCP server...")
        
        self.process = await asyncio.create_subprocess_exec(
            *self.server_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        print("✅ Server started successfully")
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a JSON-RPC request to the server"""
        self.request_id += 1
        
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        
        if params:
            request["params"] = params
        
        # Send request
        request_json = json.dumps(request) + "\n"
        
        self.process.stdin.write(request_json.encode())
        await self.process.stdin.drain()
        
        # Read response
        response_line = await self.process.stdout.readline()
        response = json.loads(response_line.decode().strip())
        
        return response
    
    async def initialize(self):
        """Initialize the MCP connection"""
        print("\n🔗 Initializing MCP connection...")
        
        response = await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {"tools": {}},
            "clientInfo": {"name": "grid-game-test-client", "version": "1.0.0"}
        })
        
        if "error" in response:
            raise Exception(f"Initialization failed: {response['error']}")
        
        print("✅ MCP connection initialized")
        return response
    
    async def list_tools(self):
        """List available tools from the server"""
        print("\n🔧 Discovering available game tools...")
        
        response = await self.send_request("tools/list")
        
        if "error" in response:
            raise Exception(f"Failed to list tools: {response['error']}")
        
        tools = response.get("result", {}).get("tools", [])
        print(f"🎮 Found {len(tools)} game tools:")
        
        for tool in tools:
            print(f"  • {tool['name']}: {tool.get('description', 'No description')}")
        
        return tools
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]):
        """Call a specific tool with arguments"""
        print(f"\n⚡ Calling tool: {name}")
        if arguments:
            print(f"   Arguments: {arguments}")
        
        response = await self.send_request("tools/call", {
            "name": name,
            "arguments": arguments
        })
        
        if "error" in response:
            print(f"❌ Tool call failed: {response['error']}")
            return None
        
        result = response.get("result", {})
        content = result.get("content", [])
        
        print("✅ Tool call successful:")
        for item in content:
            if item.get("type") == "text":
                # Print the result with proper formatting
                text = item.get("text", "")
                for line in text.split('\n'):
                    if line.strip():
                        print(f"   {line}")
        
        return result
    
    async def stop_server(self):
        """Stop the MCP server"""
        if self.process:
            print("\n🛑 Stopping server...")
            self.process.terminate()
            await self.process.wait()
            print("✅ Server stopped")

async def simulate_ai_strategic_gameplay():
    """Simulate how an AI would strategically play the grid game"""
    print("🤖 AI Grid Bot Adventure Simulation")
    print("=" * 60)
    print("This simulates how an LLM would strategically navigate and collect coins!")
    print()
    
    # Create client and start server
    client = GridGameMCPTestClient([sys.executable, "grid_game_server.py"])
    
    try:
        await client.start_server()
        await client.initialize()
        
        # Discover available tools
        tools = await client.list_tools()
        
        print("\n" + "=" * 60)
        print("🎮 Starting AI Strategic Gameplay Simulation")
        print("=" * 60)
        
        # Step 1: Start a new game
        print("\n🎯 STEP 1: Starting new grid game...")
        await client.call_tool("start_new_game", {})
        
        # Step 2: Get initial game state
        print("\n📊 STEP 2: Analyzing initial game state...")
        await client.call_tool("get_game_state", {})
        
        # Step 3: Look around for immediate threats and opportunities
        print("\n🔍 STEP 3: Scanning surroundings...")
        await client.call_tool("look_around", {})
        
        # Step 4: Analyze threats strategically
        print("\n🧠 STEP 4: Performing threat analysis...")
        await client.call_tool("analyze_threats", {})
        
        # Step 5: Find nearest coin
        print("\n🪙 STEP 5: Locating nearest coin...")
        await client.call_tool("find_nearest_coin", {})
        
        # Step 6-15: Strategic movement sequence
        strategic_moves = [
            ("north", "Moving north to avoid potential threats"),
            ("east", "Moving east toward coin cluster"),
            ("south", "Moving south to collect coin"),
            ("east", "Continuing east for next coin"),
            ("north", "Moving north to safer position"),
            ("west", "Moving west to collect golden coin"),
            ("south", "Moving south for strategic positioning"),
            ("east", "Moving east to collect remaining coins"),
            ("north", "Final positioning move"),
            ("west", "Collecting final coin")
        ]
        
        for i, (direction, strategy) in enumerate(strategic_moves, 6):
            print(f"\n🚀 STEP {i}: {strategy}")
            print(f"   Moving {direction}...")
            
            result = await client.call_tool("move_bot", {"direction": direction})
            
            # Check if game ended
            if result:
                content = result.get("content", [])
                if content and any("VICTORY" in item.get("text", "") or "GAME OVER" in item.get("text", "") 
                                 for item in content):
                    print(f"\n🎮 Game ended after move {i-5}!")
                    break
            
            # Analyze situation after each move
            if i % 3 == 0:  # Every 3rd move
                print(f"\n🧠 STEP {i}b: Re-analyzing situation...")
                await client.call_tool("analyze_threats", {})
        
        # Final game state
        print("\n📊 FINAL: Checking final game state...")
        await client.call_tool("get_game_state", {})
        
        print("\n" + "=" * 60)
        print("🎉 AI Strategic Gameplay Simulation Complete!")
        print("=" * 60)
        print("\nThis demonstrates how an LLM can:")
        print("✅ Strategically analyze grid-based game situations")
        print("✅ Plan multi-step movement sequences")
        print("✅ Assess threats and opportunities")
        print("✅ Adapt strategy based on game state changes")
        print("✅ Navigate complex spatial relationships")
        print("\n🔗 Connect to Claude Desktop to see real AI gameplay!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await client.stop_server()

async def test_individual_tools():
    """Test individual tools in isolation"""
    print("\n🔧 Individual Tool Testing")
    print("=" * 40)
    
    client = GridGameMCPTestClient([sys.executable, "grid_game_server.py"])
    
    try:
        await client.start_server()
        await client.initialize()
        
        # Test each tool individually
        test_cases = [
            ("get_game_help", {}),
            ("start_new_game", {}),
            ("get_game_state", {}),
            ("look_around", {}),
            ("analyze_threats", {}),
            ("find_nearest_coin", {}),
            ("move_bot", {"direction": "north"}),
            ("look_around", {}),  # Check surroundings after move
            ("move_bot", {"direction": "east"}),
            ("get_game_state", {}),  # Check state after moves
        ]
        
        for tool_name, arguments in test_cases:
            await client.call_tool(tool_name, arguments)
            await asyncio.sleep(0.5)  # Small delay between calls
        
        print("\n✅ All individual tool tests completed!")
        
    except Exception as e:
        print(f"❌ Individual tool test failed: {e}")
    
    finally:
        await client.stop_server()

async def test_error_handling():
    """Test error handling and edge cases"""
    print("\n🧪 Error Handling Tests")
    print("=" * 30)
    
    client = GridGameMCPTestClient([sys.executable, "grid_game_server.py"])
    
    try:
        await client.start_server()
        await client.initialize()
        
        # Test error cases
        error_test_cases = [
            ("move_bot", {"direction": "invalid"}, "Invalid direction"),
            ("move_bot", {"direction": "north"}, "Move without game started"),
            ("get_game_state", {}, "State without game"),
        ]
        
        print("\n🎮 Starting game for error tests...")
        await client.call_tool("start_new_game", {})
        
        # Test invalid moves
        invalid_moves = [
            ("move_bot", {"direction": "northwest"}, "Invalid direction"),
            ("move_bot", {"direction": ""}, "Empty direction"),
        ]
        
        for tool_name, arguments, description in invalid_moves:
            print(f"\n🧪 Testing: {description}")
            await client.call_tool(tool_name, arguments)
        
        print("\n✅ Error handling tests completed!")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
    
    finally:
        await client.stop_server()

def main():
    """Run the test suite"""
    print("🧪 Grid Bot Adventure MCP Server Test Suite")
    print("This tests the strategic grid game that LLMs can play through MCP!")
    print()
    
    try:
        # Run AI strategic gameplay simulation
        asyncio.run(simulate_ai_strategic_gameplay())
        
        print("\n" + "="*60)
        input("Press Enter to run individual tool tests...")
        
        # Run individual tool tests
        asyncio.run(test_individual_tools())
        
        print("\n" + "="*60)
        input("Press Enter to run error handling tests...")
        
        # Run error handling tests
        asyncio.run(test_error_handling())
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test suite failed: {e}")

if __name__ == "__main__":
    main()
