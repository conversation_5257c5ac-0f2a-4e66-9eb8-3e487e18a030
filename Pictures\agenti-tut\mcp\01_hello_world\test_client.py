#!/usr/bin/env python3
"""
🧪 MCP Client Test Script

This script tests the Hello World MCP server by acting as an MCP client.
It demonstrates how to:
- Connect to an MCP server
- List available tools
- Call tools with parameters
- Handle responses

Run with: python test_client.py
"""

import asyncio
import json
import subprocess
import sys
from typing import Any, Dict

class MCPTestClient:
    """Simple MCP client for testing servers"""
    
    def __init__(self, server_command: list[str]):
        self.server_command = server_command
        self.process = None
        self.request_id = 0
    
    async def start_server(self):
        """Start the MCP server process"""
        print(f"🚀 Starting MCP server: {' '.join(self.server_command)}")
        
        self.process = await asyncio.create_subprocess_exec(
            *self.server_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        print("✅ Server started successfully")
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a JSON-RPC request to the server"""
        self.request_id += 1
        
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        
        if params:
            request["params"] = params
        
        # Send request
        request_json = json.dumps(request) + "\n"
        print(f"📤 Sending: {method}")
        
        self.process.stdin.write(request_json.encode())
        await self.process.stdin.drain()
        
        # Read response
        response_line = await self.process.stdout.readline()
        response = json.loads(response_line.decode().strip())
        
        print(f"📥 Received response for {method}")
        return response
    
    async def initialize(self):
        """Initialize the MCP connection"""
        print("\n🔗 Initializing MCP connection...")
        
        response = await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        })
        
        if "error" in response:
            raise Exception(f"Initialization failed: {response['error']}")
        
        print("✅ MCP connection initialized")
        return response
    
    async def list_tools(self):
        """List available tools from the server"""
        print("\n🔧 Listing available tools...")
        
        response = await self.send_request("tools/list")
        
        if "error" in response:
            raise Exception(f"Failed to list tools: {response['error']}")
        
        tools = response.get("result", {}).get("tools", [])
        print(f"📋 Found {len(tools)} tools:")
        
        for tool in tools:
            print(f"  • {tool['name']}: {tool.get('description', 'No description')}")
        
        return tools
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]):
        """Call a specific tool with arguments"""
        print(f"\n⚡ Calling tool: {name}")
        print(f"   Arguments: {arguments}")
        
        response = await self.send_request("tools/call", {
            "name": name,
            "arguments": arguments
        })
        
        if "error" in response:
            print(f"❌ Tool call failed: {response['error']}")
            return None
        
        result = response.get("result", {})
        content = result.get("content", [])
        
        print("✅ Tool call successful:")
        for item in content:
            if item.get("type") == "text":
                print(f"   📝 {item.get('text', '')}")
        
        return result
    
    async def stop_server(self):
        """Stop the MCP server"""
        if self.process:
            print("\n🛑 Stopping server...")
            self.process.terminate()
            await self.process.wait()
            print("✅ Server stopped")

async def test_hello_server():
    """Test the Hello World MCP server"""
    print("🧪 MCP Hello World Server Test")
    print("=" * 50)
    
    # Create client and start server
    client = MCPTestClient([sys.executable, "hello_server.py"])
    
    try:
        await client.start_server()
        await client.initialize()
        
        # List available tools
        tools = await client.list_tools()
        
        # Test each tool
        test_cases = [
            ("greet", {"name": "Alice"}),
            ("greet", {"name": "  bob  "}),  # Test trimming
            ("greet", {"name": ""}),  # Test empty name
            ("greet_with_style", {"name": "Charlie", "style": "excited"}),
            ("greet_with_style", {"name": "Diana", "style": "formal"}),
            ("greet_with_style", {"name": "Eve"}),  # Test default style
            ("get_server_info", {}),
        ]
        
        for tool_name, arguments in test_cases:
            await client.call_tool(tool_name, arguments)
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("\nNext steps:")
        print("1. Try connecting this server to Claude Desktop")
        print("2. Modify the server and test your changes")
        print("3. Explore the 02_calculator example")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await client.stop_server()

def main():
    """Run the test"""
    try:
        asyncio.run(test_hello_server())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
