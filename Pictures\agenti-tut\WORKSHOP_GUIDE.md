# 🎓 Agentic AI Workshop - Instructor Guide

## 📋 Workshop Overview

**Duration:** 60 minutes  
**Audience:** Junior developers  
**Format:** Hands-on coding workshop  
**Goal:** Build a working AI agent with tools  

## ⏰ Detailed Timeline

### Phase 1: Introduction (10 minutes)

**Slides/Presentation:**
- What is an AI Agent? (3 min)
- LLMs vs Traditional Programming (2 min)
- Tools and MCP Overview (3 min)
- Workshop Goals (2 min)

**Key Points to Cover:**
- Agents can use tools, chatbots cannot
- LLMs are the "brain", tools are the "hands"
- MCP standardizes tool communication
- We'll build a real working assistant

### Phase 2: Setup & Basic Concepts (15 minutes)

**Live Coding Session:**

1. **Environment Setup (5 min)**
   ```bash
   # Show the project structure
   ls -la agenti-tut/
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Set up environment
   cp .env.example .env
   # Edit .env file with API key
   ```

2. **Test the Setup (3 min)**
   ```bash
   python test_workshop.py
   ```

3. **Explore the Code Structure (7 min)**
   - Show `agent_core.py` - the brain
   - Show `tools.py` - the superpowers
   - Explain the Tool class structure
   - Demonstrate tool registration

**Interactive Elements:**
- Ask participants to run the test script
- Have them explore the file structure
- Quick Q&A about concepts

### Phase 3: Hands-On Building (20 minutes)

**Guided Coding:**

1. **Create Your First Tool (8 min)**
   ```python
   # Live code a simple tool together
   def create_greeting_tool():
       def greet(name: str) -> str:
           return f"Hello {name}! Welcome to the AI workshop!"
       
       return Tool(
           name="greet",
           description="Greet someone by name",
           parameters={
               "type": "object",
               "properties": {
                   "name": {"type": "string", "description": "Name to greet"}
               },
               "required": ["name"]
           },
           function=greet
       )
   ```

2. **Test the Personal Assistant (7 min)**
   ```bash
   python personal_assistant.py
   ```
   
   **Demo Commands:**
   - "What's 15 * 23?"
   - "Tell me a joke"
   - "What time is it?"
   - "Generate a password"

3. **Launch the Web Interface (5 min)**
   ```bash
   streamlit run streamlit_app.py
   ```
   
   Show the beautiful web UI and let participants interact

**Participant Activities:**
- Everyone creates their own simple tool
- Test the assistant with different commands
- Explore the web interface
- Try the example prompts

### Phase 4: Advanced Features & Demo (10 minutes)

**Show Advanced Capabilities:**

1. **Multi-Tool Workflows (3 min)**
   - "Create a file called 'meeting_notes.txt' with today's date and a reminder to follow up with the client"
   - Show how the agent uses multiple tools in sequence

2. **Error Handling (2 min)**
   - Show what happens with invalid inputs
   - Demonstrate graceful error recovery

3. **Conversation Memory (2 min)**
   - Show how the agent remembers context
   - Demonstrate follow-up questions

4. **Customization Demo (3 min)**
   - Show how to modify the assistant's personality
   - Demonstrate adding new tools
   - Quick code walkthrough

### Phase 5: Wrap-up & Next Steps (5 minutes)

**Summary:**
- What we built today
- Key concepts learned
- Real-world applications

**Next Steps:**
- Resources for further learning
- Ideas for extending the project
- Community and support

## 🎯 Learning Objectives Checklist

By the end, participants should be able to:

- [ ] Explain the difference between AI agents and chatbots
- [ ] Understand how LLMs power intelligent agents
- [ ] Create a simple tool for an AI agent
- [ ] Use the Model Context Protocol for tool integration
- [ ] Run and interact with a complete AI assistant
- [ ] Identify opportunities to apply agents in their work

## 🛠️ Instructor Preparation

### Before the Workshop:

1. **Test All Code**
   ```bash
   python test_workshop.py
   ```

2. **Prepare API Keys**
   - Get Mistral AI API key
   - Test with a few example calls
   - Prepare backup keys if needed

3. **Set Up Demo Environment**
   - Clean workspace
   - Test internet connectivity
   - Prepare screen sharing

4. **Review Common Issues**
   - API key problems
   - Package installation issues
   - Network connectivity problems

### During the Workshop:

1. **Encourage Participation**
   - Ask questions frequently
   - Have participants code along
   - Share screens for help

2. **Handle Questions**
   - Keep a "parking lot" for complex questions
   - Encourage peer helping
   - Provide clear, simple explanations

3. **Manage Time**
   - Use timers for each section
   - Have backup activities if ahead
   - Skip advanced features if behind

## 🚨 Troubleshooting Guide

### Common Issues:

1. **"ModuleNotFoundError"**
   ```bash
   pip install -r requirements.txt
   ```

2. **"API Key Error"**
   - Check .env file exists
   - Verify API key is correct
   - Test with curl command

3. **"Streamlit won't start"**
   ```bash
   streamlit --version
   pip install --upgrade streamlit
   ```

4. **"Agent not responding"**
   - Check internet connection
   - Verify API key has credits
   - Try simpler prompts

### Backup Plans:

1. **No Internet:** Use offline demos and code review
2. **API Issues:** Show pre-recorded demos
3. **Time Constraints:** Focus on core concepts, skip web UI
4. **Technical Problems:** Pair programming approach

## 📊 Success Metrics

**Immediate (End of Workshop):**
- 90% of participants successfully run the assistant
- 80% create their own simple tool
- 100% understand the core concepts

**Follow-up (1 week later):**
- 50% have experimented with the code at home
- 30% have started their own agent project
- 20% have shared their experience with others

## 🎁 Take-Home Materials

Participants get:
- Complete workshop code
- Detailed README with examples
- List of additional resources
- Ideas for next projects
- Contact information for questions

## 🔄 Workshop Variations

### 30-Minute Version:
- Skip web interface
- Focus on core concepts only
- Simplified tool creation

### 90-Minute Version:
- Add external API integration
- Build more complex tools
- Cover agent orchestration

### Advanced Version:
- Multi-agent systems
- Memory and persistence
- Production deployment

---

**Remember:** The goal is to inspire and educate, not to overwhelm. Keep it fun, interactive, and practical! 🚀
