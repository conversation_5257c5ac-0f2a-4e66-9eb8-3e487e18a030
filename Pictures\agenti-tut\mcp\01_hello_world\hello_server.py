#!/usr/bin/env python3
"""
🌟 Hello World MCP Server

This is a real Model Context Protocol server using the official Python SDK.
It demonstrates the simplest possible MCP server with a greeting tool.

This server can be used with:
- Claude Desktop
- Any MCP client
- Custom applications

Run with: python hello_server.py
"""

import asyncio
from mcp.server.fastmcp import FastMCP

# Create MCP server instance
mcp = FastMCP("Hello World Server")

@mcp.tool()
async def greet(name: str) -> str:
    """
    Greet someone by name with a friendly message.
    
    Args:
        name: The person's name to greet
        
    Returns:
        A friendly greeting message
    """
    if not name or not name.strip():
        return "Error: Please provide a name to greet!"
    
    clean_name = name.strip().title()
    return f"Hello {clean_name}! Welcome to the world of MCP! 🌟"

@mcp.tool()
async def greet_with_style(name: str, style: str = "friendly") -> str:
    """
    Greet someone with different personality styles.
    
    Args:
        name: The person's name to greet
        style: The greeting style (friendly, formal, casual, excited)
        
    Returns:
        A greeting in the specified style
    """
    if not name or not name.strip():
        return "Error: Please provide a name to greet!"
    
    clean_name = name.strip().title()
    
    styles = {
        "friendly": f"Hello {clean_name}! Welcome to MC<PERSON>! 🤖✨",
        "formal": f"Good day, {clean_name}. Welcome to our MCP demonstration.",
        "casual": f"Hey {clean_name}! Ready to explore MCP? 😎",
        "excited": f"WOW! Hi {clean_name}! This MCP stuff is AMAZING! 🚀🎉",
        "mysterious": f"Greetings, {clean_name}... You have entered the realm of MCP... 🔮"
    }
    
    return styles.get(style.lower(), styles["friendly"])

@mcp.tool()
async def get_server_info() -> str:
    """
    Get information about this MCP server.
    
    Returns:
        Server information and capabilities
    """
    return """
🌟 Hello World MCP Server

This is a demonstration MCP server built with the official Python SDK.

Available Tools:
• greet - Simple greeting by name
• greet_with_style - Greeting with personality styles
• get_server_info - This information

MCP Features Demonstrated:
✅ Tool discovery and execution
✅ Parameter validation
✅ Multiple tools in one server
✅ JSON-RPC communication
✅ Compatible with Claude Desktop

Built with: mcp Python SDK
Protocol: Model Context Protocol (MCP)
"""

def main():
    """Run the MCP server"""
    print("🌟 Starting Hello World MCP Server...")
    print("Server is ready and listening for MCP connections.")
    print("Use Ctrl+C to stop the server.")
    
    # Run the server with stdio transport
    # This allows communication via standard input/output
    mcp.run(transport="stdio")

if __name__ == "__main__":
    main()
