# 🤖 Agentic AI Workshop - Build Your First AI Agent

Welcome to the **Agentic AI Workshop**! This hands-on tutorial will teach you how to build intelligent AI agents that can use tools to interact with the real world.

## 🎯 What You'll Learn

- **AI Agents vs Chatbots**: Understanding the key differences
- **Large Language Models (LLMs)**: How they power intelligent agents
- **Tool Integration**: Giving your agent superpowers
- **Model Context Protocol (MCP)**: Standardized tool communication
- **Practical Implementation**: Build a real personal assistant

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Clone or download the workshop files
cd agenti-tut

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env and add your Mistral API key
```

### 2. Get Your API Key

1. Visit [Mistral AI Console](https://console.mistral.ai/)
2. Sign up for a free account
3. Generate an API key
4. Add it to your `.env` file

### 3. Run the Workshop

**Option A: Interactive Terminal**
```bash
python personal_assistant.py
```

**Option B: Web Interface**
```bash
streamlit run streamlit_app.py
```

## 📁 Project Structure

```
agenti-tut/
├── 📖 agentic_ai_workshop.md      # Main tutorial content
├── 📖 setup_and_concepts.md       # Deep dive into concepts
├── 🤖 agent_core.py               # Core agent framework
├── 🔧 tools.py                    # Collection of agent tools
├── 🎯 personal_assistant.py       # Complete assistant example
├── 🌐 streamlit_app.py            # Web interface
├── 📦 requirements.txt            # Python dependencies
├── ⚙️ .env.example                # Environment template
└── 📚 README.md                   # This file
```

## 🛠️ Workshop Components

### 1. Core Agent Framework (`agent_core.py`)
- Base `MistralAgent` class
- Tool management system
- Conversation handling
- Error management

### 2. Tool Collection (`tools.py`)
- **Calculator**: Math operations
- **Weather**: Weather information
- **File System**: Read/write files
- **Time Management**: Current time, reminders
- **Fun Tools**: Jokes, passwords, coin flip

### 3. Personal Assistant (`personal_assistant.py`)
- Complete working example
- Interactive command-line interface
- Friendly personality
- Help system and demos

### 4. Web Interface (`streamlit_app.py`)
- Beautiful web UI
- Real-time chat interface
- Tool usage visualization
- Example prompts

## 🎮 Try These Examples

Once your assistant is running, try these commands:

```
# Math and calculations
"What's 15 * 23 + 7?"
"Calculate the square root of 144"

# Weather information
"What's the weather in Tokyo?"
"How's the weather in London?"

# File management
"List files in this directory"
"Create a file called test.txt with hello world"
"Read the contents of README.md"

# Time and scheduling
"What time is it?"
"Set a reminder to take a break in 30 minutes"
"How many days between 2024-01-01 and 2024-12-31?"

# Fun and utilities
"Tell me a programming joke"
"Generate a secure password"
"Flip a coin"
```

## 🧠 Key Concepts Explained

### AI Agents vs Traditional Chatbots

**Traditional Chatbot:**
- Pre-programmed responses
- No external interaction
- Limited to training data

**AI Agent:**
- Dynamic tool usage
- Real-world interaction
- Autonomous decision making

### How Tools Work

```python
# Tool definition
def get_weather(location: str) -> str:
    # Implementation here
    return f"Weather in {location}: 22°C, sunny"

# Agent uses tool automatically
user: "What's the weather in Paris?"
agent: *calls get_weather("Paris")* 
agent: "The weather in Paris is 22°C and sunny!"
```

### Model Context Protocol (MCP)

MCP provides a standardized way for AI models to:
- Discover available tools
- Understand tool parameters
- Execute tools safely
- Handle responses

## 🔧 Extending the Workshop

### Add New Tools

```python
def create_my_tool() -> Tool:
    def my_function(param: str) -> str:
        return f"Result: {param}"
    
    return Tool(
        name="my_tool",
        description="Description of what it does",
        parameters={
            "type": "object",
            "properties": {
                "param": {
                    "type": "string",
                    "description": "Parameter description"
                }
            },
            "required": ["param"]
        },
        function=my_function
    )
```

### Customize the Assistant

1. **Change Personality**: Edit the system prompt in `PersonalAssistant.__init__()`
2. **Add Tools**: Create new tools in `tools.py`
3. **Modify UI**: Customize the Streamlit interface
4. **Add Features**: Extend the core agent framework

## 🎓 Learning Path

### Beginner (30 minutes)
1. Read the main tutorial
2. Run the basic examples
3. Try the personal assistant

### Intermediate (1 hour)
1. Understand the code structure
2. Modify existing tools
3. Create a simple custom tool

### Advanced (2+ hours)
1. Build complex multi-step tools
2. Add external API integrations
3. Create specialized agent types

## 🌟 Next Steps

After completing this workshop:

1. **Explore Real APIs**: Integrate with actual weather, calendar, or email services
2. **Build Specialized Agents**: Create agents for specific domains (coding, research, etc.)
3. **Learn Advanced Patterns**: Study agent orchestration, memory systems, and planning
4. **Join the Community**: Share your creations and learn from others

## 📚 Additional Resources

### Documentation
- [Mistral AI Documentation](https://docs.mistral.ai/)
- [OpenAI Function Calling](https://platform.openai.com/docs/guides/function-calling)
- [Streamlit Documentation](https://docs.streamlit.io/)

### Advanced Topics
- **LangChain**: Framework for LLM applications
- **AutoGPT**: Autonomous AI agents
- **CrewAI**: Multi-agent systems
- **Semantic Kernel**: Microsoft's agent framework

### Community
- [r/MachineLearning](https://reddit.com/r/MachineLearning)
- [AI Agent Discord Communities](https://discord.gg/ai)
- [GitHub AI Agent Projects](https://github.com/topics/ai-agent)

## 🤝 Contributing

Found a bug or have an improvement? Contributions welcome!

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This workshop is open source and available under the MIT License.

---

**Happy coding! 🚀 Build amazing AI agents!**
