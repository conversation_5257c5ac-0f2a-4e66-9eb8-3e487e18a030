# 🔧 Setup & Deep Dive into Concepts

## 📦 Environment Setup

### 1. Install Required Packages

```bash
pip install openai requests python-dotenv streamlit
```

### 2. Create Environment File

Create a `.env` file in your project directory:

```env
# .env
MISTRAL_API_KEY=your_mistral_api_key_here
```

### 3. Project Structure

```
agenti-tut/
├── .env
├── requirements.txt
├── agent_core.py
├── tools.py
├── personal_assistant.py
└── streamlit_app.py
```

---

## 🧠 Deep Dive: How AI Agents Think

### The Agent Loop

```python
def agent_loop():
    while True:
        # 1. Perceive - Get input from user or environment
        user_input = get_user_input()
        
        # 2. Think - Use LLM to understand and plan
        plan = llm.generate_plan(user_input, available_tools)
        
        # 3. Act - Execute tools based on the plan
        results = execute_tools(plan.tool_calls)
        
        # 4. Reflect - Evaluate results and respond
        response = llm.generate_response(results)
        
        # 5. Communicate - Send response back to user
        send_response(response)
```

### Example: Agent Thinking Process

**User:** "Schedule a meeting with <PERSON> for tomorrow at 2 PM"

**Agent's Internal Process:**
1. **Parse Intent:** User wants to schedule a meeting
2. **Identify Required Info:** 
   - Person: Sarah
   - Time: Tomorrow at 2 PM
   - Action: Schedule meeting
3. **Plan Tools:**
   - `get_contact_info("Sarah")` - Find Sarah's email
   - `check_calendar("tomorrow 2 PM")` - Check availability
   - `send_calendar_invite(...)` - Create meeting
4. **Execute & Respond**

---

## 🔧 Understanding Tool Integration

### Tool Definition Schema

```python
from typing import Dict, Any, List
from dataclasses import dataclass

@dataclass
class Tool:
    name: str
    description: str
    parameters: Dict[str, Any]
    function: callable
    
    def to_openai_format(self) -> Dict[str, Any]:
        """Convert to OpenAI function calling format"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }
```

### Example Tool Implementation

```python
def create_weather_tool():
    def get_weather(location: str) -> str:
        """Get current weather for a location"""
        # Simulate API call
        import random
        temps = [18, 22, 25, 28, 15, 20]
        conditions = ["sunny", "cloudy", "rainy", "partly cloudy"]
        
        temp = random.choice(temps)
        condition = random.choice(conditions)
        
        return f"Weather in {location}: {temp}°C and {condition}"
    
    return Tool(
        name="get_weather",
        description="Get current weather information for any location",
        parameters={
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The city or location to get weather for"
                }
            },
            "required": ["location"]
        },
        function=get_weather
    )
```

---

## 🤖 LLM Integration with Mistral

### Setting up the LLM Client

```python
import os
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()

class MistralAgent:
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv("MISTRAL_API_KEY"),
            base_url="https://codestral.mistral.ai/v1"
        )
        self.model = "codestral-latest"
        self.tools = []
        self.conversation_history = []
    
    def add_tool(self, tool: Tool):
        """Add a tool to the agent's toolkit"""
        self.tools.append(tool)
    
    def chat(self, message: str) -> str:
        """Main chat interface"""
        # Add user message to history
        self.conversation_history.append({
            "role": "user", 
            "content": message
        })
        
        # Prepare tools for API call
        tools_schema = [tool.to_openai_format() for tool in self.tools]
        
        # Call LLM
        response = self.client.chat.completions.create(
            model=self.model,
            messages=self.conversation_history,
            tools=tools_schema if tools_schema else None,
            tool_choice="auto" if tools_schema else None
        )
        
        return self._process_response(response)
```

---

## 🔄 Tool Execution Flow

### How Agents Execute Tools

```python
def _process_response(self, response):
    """Process LLM response and execute any tool calls"""
    message = response.choices[0].message
    
    # Add assistant message to history
    self.conversation_history.append({
        "role": "assistant",
        "content": message.content,
        "tool_calls": message.tool_calls
    })
    
    # Execute tool calls if any
    if message.tool_calls:
        for tool_call in message.tool_calls:
            result = self._execute_tool(tool_call)
            
            # Add tool result to conversation
            self.conversation_history.append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": result
            })
        
        # Get final response after tool execution
        final_response = self.client.chat.completions.create(
            model=self.model,
            messages=self.conversation_history
        )
        
        return final_response.choices[0].message.content
    
    return message.content

def _execute_tool(self, tool_call):
    """Execute a specific tool call"""
    tool_name = tool_call.function.name
    tool_args = json.loads(tool_call.function.arguments)
    
    # Find the tool
    tool = next((t for t in self.tools if t.name == tool_name), None)
    if not tool:
        return f"Error: Tool {tool_name} not found"
    
    try:
        # Execute the tool function
        result = tool.function(**tool_args)
        return str(result)
    except Exception as e:
        return f"Error executing {tool_name}: {str(e)}"
```

---

## 🎯 Key Concepts Summary

### 1. **Agents vs Chatbots**
- **Chatbots:** Static responses, no external interaction
- **Agents:** Dynamic, tool-using, goal-oriented

### 2. **LLM Role**
- **Brain** of the agent
- Understands context and intent
- Plans tool usage
- Generates natural responses

### 3. **Tools**
- **Hands** of the agent
- Enable real-world interaction
- Extend agent capabilities
- Must be well-documented for LLM

### 4. **MCP Benefits**
- Standardized tool interface
- Safe execution environment
- Easy tool discovery
- Cross-platform compatibility

---

## 🚀 Ready for Hands-On Coding?

Now that you understand the concepts, let's build some real code! In the next section, we'll create:

1. A basic agent framework
2. Several useful tools
3. A complete personal assistant agent

Let's start coding! 💻
