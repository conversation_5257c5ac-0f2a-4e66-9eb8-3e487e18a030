"""
🎲 Random Tools - MCP Examples for Random Generation

This demonstrates random generation tools with various patterns:
- Random numbers with ranges
- Random choices from lists
- Decision makers
- Fun random generators
"""

import sys
import os
import random
import string

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from agent_core import Tool

def create_random_number_tool() -> Tool:
    """Generate random numbers in a specified range"""
    
    def random_number(min_val: int = 1, max_val: int = 100) -> str:
        """
        Generate a random number between min_val and max_val (inclusive)
        
        Args:
            min_val: Minimum value (default: 1)
            max_val: Maximum value (default: 100)
            
        Returns:
            Random number with range info
        """
        try:
            if min_val > max_val:
                return f"❌ Error: Minimum value ({min_val}) cannot be greater than maximum value ({max_val})"
            
            if max_val - min_val > 1000000:
                return "❌ Error: Range is too large (max 1,000,000)"
            
            result = random.randint(min_val, max_val)
            
            return f"🎲 Random number between {min_val} and {max_val}: **{result}**"
            
        except Exception as e:
            return f"❌ Error generating random number: {str(e)}"
    
    return Tool(
        name="random_number",
        description="Generate a random number within a specified range",
        parameters={
            "type": "object",
            "properties": {
                "min_val": {
                    "type": "integer",
                    "description": "Minimum value (inclusive, default: 1)"
                },
                "max_val": {
                    "type": "integer",
                    "description": "Maximum value (inclusive, default: 100)"
                }
            },
            "required": []
        },
        function=random_number
    )

def create_random_choice_tool() -> Tool:
    """Pick a random option from a list"""
    
    def random_choice(options: str) -> str:
        """
        Pick a random option from a comma-separated list
        
        Args:
            options: Comma-separated list of options
            
        Returns:
            Randomly selected option
        """
        try:
            # Parse options
            option_list = [opt.strip() for opt in options.split(',') if opt.strip()]
            
            if not option_list:
                return "❌ Error: Please provide at least one option"
            
            if len(option_list) == 1:
                return f"🎯 Only one option provided: **{option_list[0]}**"
            
            # Pick random choice
            choice = random.choice(option_list)
            
            # Fun emojis for different types of choices
            food_emojis = ['🍕', '🍔', '🍜', '🌮', '🍣', '🥗', '🍝', '🥙']
            activity_emojis = ['🎮', '📚', '🎬', '🏃‍♂️', '🎨', '🎵', '🧘‍♀️', '🛍️']
            
            # Try to detect food or activity choices
            food_words = ['pizza', 'burger', 'sushi', 'pasta', 'salad', 'tacos', 'ramen']
            activity_words = ['movie', 'game', 'read', 'exercise', 'music', 'shop', 'walk']
            
            emoji = '🎯'
            if any(word in choice.lower() for word in food_words):
                emoji = random.choice(food_emojis)
            elif any(word in choice.lower() for word in activity_words):
                emoji = random.choice(activity_emojis)
            
            return f"{emoji} I choose: **{choice}**!\n\n_Selected from {len(option_list)} options: {', '.join(option_list)}_"
            
        except Exception as e:
            return f"❌ Error making choice: {str(e)}"
    
    return Tool(
        name="random_choice",
        description="Pick a random option from a comma-separated list of choices",
        parameters={
            "type": "object",
            "properties": {
                "options": {
                    "type": "string",
                    "description": "Comma-separated list of options to choose from (e.g., 'pizza, burger, sushi, tacos')"
                }
            },
            "required": ["options"]
        },
        function=random_choice
    )

def create_decision_maker_tool() -> Tool:
    """Make random yes/no decisions"""
    
    def make_decision(question: str) -> str:
        """
        Make a random yes/no decision for a question
        
        Args:
            question: The question to decide on
            
        Returns:
            Random decision with encouragement
        """
        try:
            if not question.strip():
                return "❌ Error: Please provide a question to decide on"
            
            # Clean up the question
            question = question.strip()
            if not question.endswith('?'):
                question += '?'
            
            # Make random decision
            decision = random.choice([True, False])
            
            if decision:
                responses = [
                    "YES! Go for it! 🚀",
                    "Absolutely! Why not? ✨",
                    "YES! Life is short! 🌟",
                    "Do it! You've got this! 💪",
                    "YES! Take the leap! 🦘",
                    "Definitely! Seize the day! ☀️"
                ]
                answer = random.choice(responses)
                emoji = "✅"
            else:
                responses = [
                    "Not right now. Maybe later? 🤔",
                    "Nah, skip it this time. 😌",
                    "No, focus on something else. 🎯",
                    "Not today. Save your energy! 💤",
                    "No, trust your instincts. 🧘‍♀️",
                    "Skip it. Something better awaits! 🌈"
                ]
                answer = random.choice(responses)
                emoji = "❌"
            
            return f"{emoji} **Decision for '{question}'**\n\n{answer}"
            
        except Exception as e:
            return f"❌ Error making decision: {str(e)}"
    
    return Tool(
        name="make_decision",
        description="Make a random yes/no decision for any question",
        parameters={
            "type": "object",
            "properties": {
                "question": {
                    "type": "string",
                    "description": "The question or decision you need help with"
                }
            },
            "required": ["question"]
        },
        function=make_decision
    )

def create_dice_roller_tool() -> Tool:
    """Roll dice with various configurations"""
    
    def roll_dice(num_dice: int = 1, num_sides: int = 6) -> str:
        """
        Roll one or more dice
        
        Args:
            num_dice: Number of dice to roll (default: 1)
            num_sides: Number of sides per die (default: 6)
            
        Returns:
            Dice roll results
        """
        try:
            if num_dice < 1 or num_dice > 20:
                return "❌ Error: Number of dice must be between 1 and 20"
            
            if num_sides < 2 or num_sides > 100:
                return "❌ Error: Number of sides must be between 2 and 100"
            
            # Roll the dice
            rolls = [random.randint(1, num_sides) for _ in range(num_dice)]
            total = sum(rolls)
            
            # Format the result
            if num_dice == 1:
                return f"🎲 Rolled 1d{num_sides}: **{rolls[0]}**"
            else:
                rolls_str = ', '.join(map(str, rolls))
                return f"🎲 Rolled {num_dice}d{num_sides}: [{rolls_str}] = **{total}**"
                
        except Exception as e:
            return f"❌ Error rolling dice: {str(e)}"
    
    return Tool(
        name="roll_dice",
        description="Roll one or more dice with customizable number of sides",
        parameters={
            "type": "object",
            "properties": {
                "num_dice": {
                    "type": "integer",
                    "description": "Number of dice to roll (1-20, default: 1)"
                },
                "num_sides": {
                    "type": "integer",
                    "description": "Number of sides per die (2-100, default: 6)"
                }
            },
            "required": []
        },
        function=roll_dice
    )

def create_password_generator_tool() -> Tool:
    """Generate secure random passwords"""
    
    def generate_password(length: int = 12, include_symbols: bool = True) -> str:
        """
        Generate a secure random password
        
        Args:
            length: Password length (default: 12)
            include_symbols: Whether to include symbols (default: True)
            
        Returns:
            Generated password with strength info
        """
        try:
            if length < 4:
                return "❌ Error: Password length must be at least 4 characters"
            
            if length > 50:
                return "❌ Error: Password length cannot exceed 50 characters"
            
            # Character sets
            lowercase = string.ascii_lowercase
            uppercase = string.ascii_uppercase
            digits = string.digits
            symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?" if include_symbols else ""
            
            # Ensure at least one character from each required set
            password = [
                random.choice(lowercase),
                random.choice(uppercase),
                random.choice(digits)
            ]
            
            if include_symbols:
                password.append(random.choice(symbols))
            
            # Fill the rest with random characters
            all_chars = lowercase + uppercase + digits + symbols
            remaining_length = length - len(password)
            
            for _ in range(remaining_length):
                password.append(random.choice(all_chars))
            
            # Shuffle the password
            random.shuffle(password)
            final_password = ''.join(password)
            
            # Assess strength
            strength = "Strong" if length >= 12 and include_symbols else "Medium"
            if length < 8:
                strength = "Weak"
            
            symbol_info = "with symbols" if include_symbols else "without symbols"
            
            return f"🔐 Generated password ({length} chars, {symbol_info}):\n\n**{final_password}**\n\n_Strength: {strength}_"
            
        except Exception as e:
            return f"❌ Error generating password: {str(e)}"
    
    return Tool(
        name="generate_password",
        description="Generate a secure random password with customizable length and character sets",
        parameters={
            "type": "object",
            "properties": {
                "length": {
                    "type": "integer",
                    "description": "Password length (4-50 characters, default: 12)"
                },
                "include_symbols": {
                    "type": "boolean",
                    "description": "Whether to include symbols like !@#$%^&* (default: true)"
                }
            },
            "required": []
        },
        function=generate_password
    )

def get_all_random_tools():
    """Get all random tools"""
    return [
        create_random_number_tool(),
        create_random_choice_tool(),
        create_decision_maker_tool(),
        create_dice_roller_tool(),
        create_password_generator_tool()
    ]

def test_random_tools():
    """Test all random tools"""
    print("🎲 Testing Random Tools")
    print("=" * 50)
    
    tools = get_all_random_tools()
    
    test_cases = [
        ("random_number", {"min_val": 1, "max_val": 10}),
        ("random_choice", {"options": "pizza, burger, sushi, tacos"}),
        ("make_decision", {"question": "Should I learn AI programming"}),
        ("roll_dice", {"num_dice": 2, "num_sides": 6}),
        ("generate_password", {"length": 16, "include_symbols": True}),
    ]
    
    for tool_name, params in test_cases:
        tool = next((t for t in tools if t.name == tool_name), None)
        if tool:
            print(f"\n🔧 Testing {tool_name}:")
            print(f"   Input: {params}")
            result = tool.function(**params)
            print(f"   Output: {result}")

if __name__ == "__main__":
    test_random_tools()
