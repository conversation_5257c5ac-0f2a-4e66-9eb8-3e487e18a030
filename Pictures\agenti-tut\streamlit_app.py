"""
🌐 Streamlit Web Interface for Personal Assistant
A beautiful web UI for interacting with your AI agent!
"""

import streamlit as st
import os
from datetime import datetime
from personal_assistant import PersonalAssistant

# Page configuration
st.set_page_config(
    page_title="AI Personal Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .tool-usage {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
        font-style: italic;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'assistant' not in st.session_state:
        st.session_state.assistant = None
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'assistant_ready' not in st.session_state:
        st.session_state.assistant_ready = False

def create_assistant():
    """Create and cache the assistant"""
    if st.session_state.assistant is None:
        try:
            with st.spinner("🤖 Initializing AI Assistant..."):
                st.session_state.assistant = PersonalAssistant()
                st.session_state.assistant_ready = True
            st.success("✅ Assistant ready!")
        except Exception as e:
            st.error(f"❌ Error creating assistant: {str(e)}")
            st.error("Make sure you have set up your MISTRAL_API_KEY in the .env file")
            return False
    return True

def display_chat_history():
    """Display the chat history"""
    for message in st.session_state.chat_history:
        if message['role'] == 'user':
            st.markdown(f"""
            <div class="chat-message user-message">
                <strong>👤 You:</strong> {message['content']}
            </div>
            """, unsafe_allow_html=True)
        elif message['role'] == 'assistant':
            st.markdown(f"""
            <div class="chat-message assistant-message">
                <strong>🤖 Alex:</strong> {message['content']}
            </div>
            """, unsafe_allow_html=True)
        elif message['role'] == 'tool':
            st.markdown(f"""
            <div class="chat-message tool-usage">
                <strong>🔧 Tool Used:</strong> {message['content']}
            </div>
            """, unsafe_allow_html=True)

def main():
    """Main Streamlit application"""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🤖 AI Personal Assistant</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar
    with st.sidebar:
        st.header("🎛️ Control Panel")
        
        # API Key check
        api_key_status = "✅ Ready" if os.getenv("MISTRAL_API_KEY") else "❌ Missing"
        st.metric("API Key Status", api_key_status)
        
        if not os.getenv("MISTRAL_API_KEY"):
            st.error("Please add MISTRAL_API_KEY to your .env file")
            st.stop()
        
        # Initialize assistant
        if st.button("🚀 Initialize Assistant"):
            create_assistant()
        
        # Assistant status
        if st.session_state.assistant_ready:
            st.success("Assistant Status: Ready")
            
            # Tools info
            if st.session_state.assistant:
                tools = st.session_state.assistant.list_tools()
                st.metric("Available Tools", len(tools))
                
                with st.expander("🔧 View Tools"):
                    for tool in tools:
                        st.write(f"• {tool}")
        
        # Chat controls
        st.header("💬 Chat Controls")
        
        if st.button("🧹 Clear Chat"):
            st.session_state.chat_history = []
            if st.session_state.assistant:
                st.session_state.assistant.clear_history()
            st.rerun()
        
        if st.button("📊 Show Summary"):
            if st.session_state.assistant:
                summary = st.session_state.assistant.get_conversation_summary()
                st.text(summary)
        
        # Example prompts
        st.header("💡 Try These Examples")
        example_prompts = [
            "What's 15 * 23 + 7?",
            "What's the weather in Tokyo?",
            "Tell me a programming joke",
            "Generate a secure password",
            "What time is it?",
            "List files in this directory"
        ]
        
        for prompt in example_prompts:
            if st.button(f"💬 {prompt}", key=f"example_{prompt}"):
                st.session_state.example_prompt = prompt
    
    # Main chat interface
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.header("💬 Chat with Alex")
        
        # Check if assistant is ready
        if not st.session_state.assistant_ready:
            st.info("👆 Please initialize the assistant using the sidebar")
            st.stop()
        
        # Display chat history
        chat_container = st.container()
        with chat_container:
            display_chat_history()
        
        # Chat input
        user_input = st.chat_input("Type your message here...")
        
        # Handle example prompt
        if 'example_prompt' in st.session_state:
            user_input = st.session_state.example_prompt
            del st.session_state.example_prompt
        
        # Process user input
        if user_input:
            # Add user message to history
            st.session_state.chat_history.append({
                'role': 'user',
                'content': user_input,
                'timestamp': datetime.now()
            })
            
            # Get assistant response
            try:
                with st.spinner("🤖 Alex is thinking..."):
                    response = st.session_state.assistant.chat(user_input)
                
                # Add assistant response to history
                st.session_state.chat_history.append({
                    'role': 'assistant',
                    'content': response,
                    'timestamp': datetime.now()
                })
                
                # Rerun to update the display
                st.rerun()
                
            except Exception as e:
                st.error(f"❌ Error: {str(e)}")
    
    with col2:
        st.header("📚 Quick Help")
        
        help_info = """
        **How to use:**
        1. Initialize the assistant
        2. Type your message
        3. See Alex respond with tools!
        
        **Alex can help with:**
        • 🧮 Math calculations
        • 🌤️ Weather information
        • 📁 File management
        • ⏰ Time & reminders
        • 😄 Programming jokes
        • 🔐 Password generation
        • And much more!
        
        **Tips:**
        • Be specific in your requests
        • Try the example prompts
        • Ask follow-up questions
        • Use the clear button to start fresh
        """
        
        st.markdown(help_info)
        
        # Fun stats
        if st.session_state.chat_history:
            st.header("📊 Chat Stats")
            user_messages = len([m for m in st.session_state.chat_history if m['role'] == 'user'])
            assistant_messages = len([m for m in st.session_state.chat_history if m['role'] == 'assistant'])
            
            st.metric("Your Messages", user_messages)
            st.metric("Alex's Responses", assistant_messages)

if __name__ == "__main__":
    main()
