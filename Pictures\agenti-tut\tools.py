"""
Agent Tools Collection - The Agent's Superpowers!
This file contains various tools that our AI agent can use.
"""

import os
import json
import random
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any
from agent_core import Tool

# =============================================================================
# UTILITY TOOLS
# =============================================================================

def create_calculator_tool() -> Tool:
    """A calculator tool for mathematical operations"""
    
    def calculate(expression: str) -> str:
        """Safely evaluate mathematical expressions"""
        try:
            # Only allow safe mathematical operations
            allowed_chars = set('0123456789+-*/.() ')
            if not all(c in allowed_chars for c in expression):
                return "Error: Only basic math operations are allowed (+, -, *, /, parentheses)"
            
            result = eval(expression)
            return f"Result: {expression} = {result}"
        except Exception as e:
            return f"Error calculating '{expression}': {str(e)}"
    
    return Tool(
        name="calculate",
        description="Perform mathematical calculations with basic operations (+, -, *, /, parentheses)",
        parameters={
            "type": "object",
            "properties": {
                "expression": {
                    "type": "string",
                    "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4')"
                }
            },
            "required": ["expression"]
        },
        function=calculate
    )

def create_weather_tool() -> Tool:
    """A simulated weather tool (in real app, would use actual weather API)"""
    
    def get_weather(location: str) -> str:
        """Get current weather for a location"""
        # Simulate realistic weather data
        cities_weather = {
            "london": {"temp": 15, "condition": "rainy", "humidity": 80},
            "paris": {"temp": 18, "condition": "cloudy", "humidity": 65},
            "tokyo": {"temp": 22, "condition": "sunny", "humidity": 55},
            "new york": {"temp": 20, "condition": "partly cloudy", "humidity": 60},
            "sydney": {"temp": 25, "condition": "sunny", "humidity": 50},
        }
        
        location_lower = location.lower()
        if location_lower in cities_weather:
            weather = cities_weather[location_lower]
            return f"Weather in {location}: {weather['temp']}°C, {weather['condition']}, humidity {weather['humidity']}%"
        else:
            # Generate random weather for unknown cities
            temp = random.randint(10, 30)
            conditions = ["sunny", "cloudy", "rainy", "partly cloudy", "windy"]
            condition = random.choice(conditions)
            humidity = random.randint(40, 90)
            return f"Weather in {location}: {temp}°C, {condition}, humidity {humidity}%"
    
    return Tool(
        name="get_weather",
        description="Get current weather information for any city or location",
        parameters={
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The city or location to get weather for"
                }
            },
            "required": ["location"]
        },
        function=get_weather
    )

# =============================================================================
# FILE SYSTEM TOOLS
# =============================================================================

def create_file_tools() -> List[Tool]:
    """Create file system tools for reading and writing files"""
    
    def read_file(filename: str) -> str:
        """Read contents of a file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            return f"File '{filename}' contents:\n{content}"
        except FileNotFoundError:
            return f"Error: File '{filename}' not found"
        except Exception as e:
            return f"Error reading file '{filename}': {str(e)}"
    
    def write_file(filename: str, content: str) -> str:
        """Write content to a file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return f"Successfully wrote content to '{filename}'"
        except Exception as e:
            return f"Error writing to file '{filename}': {str(e)}"
    
    def list_files(directory: str = ".") -> str:
        """List files in a directory"""
        try:
            files = os.listdir(directory)
            if not files:
                return f"Directory '{directory}' is empty"
            
            file_list = "\n".join(f"- {file}" for file in sorted(files))
            return f"Files in '{directory}':\n{file_list}"
        except Exception as e:
            return f"Error listing files in '{directory}': {str(e)}"
    
    return [
        Tool(
            name="read_file",
            description="Read the contents of a text file",
            parameters={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "Path to the file to read"
                    }
                },
                "required": ["filename"]
            },
            function=read_file
        ),
        Tool(
            name="write_file",
            description="Write content to a text file",
            parameters={
                "type": "object",
                "properties": {
                    "filename": {
                        "type": "string",
                        "description": "Path to the file to write"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write to the file"
                    }
                },
                "required": ["filename", "content"]
            },
            function=write_file
        ),
        Tool(
            name="list_files",
            description="List all files in a directory",
            parameters={
                "type": "object",
                "properties": {
                    "directory": {
                        "type": "string",
                        "description": "Directory path to list files from (default: current directory)"
                    }
                },
                "required": []
            },
            function=list_files
        )
    ]

# =============================================================================
# TIME AND SCHEDULING TOOLS
# =============================================================================

def create_time_tools() -> List[Tool]:
    """Create time-related tools"""
    
    def get_current_time() -> str:
        """Get the current date and time"""
        now = datetime.now()
        return f"Current time: {now.strftime('%Y-%m-%d %H:%M:%S (%A)')}"
    
    def set_reminder(message: str, minutes: int) -> str:
        """Set a reminder (simulated - in real app would use actual scheduling)"""
        reminder_time = datetime.now() + timedelta(minutes=minutes)
        return f"Reminder set: '{message}' for {reminder_time.strftime('%Y-%m-%d %H:%M:%S')} ({minutes} minutes from now)"
    
    def calculate_time_difference(date1: str, date2: str) -> str:
        """Calculate difference between two dates"""
        try:
            # Try to parse dates in common formats
            formats = ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%m/%d/%Y', '%d/%m/%Y']
            
            dt1 = dt2 = None
            for fmt in formats:
                try:
                    dt1 = datetime.strptime(date1, fmt)
                    break
                except ValueError:
                    continue
            
            for fmt in formats:
                try:
                    dt2 = datetime.strptime(date2, fmt)
                    break
                except ValueError:
                    continue
            
            if dt1 is None or dt2 is None:
                return "Error: Could not parse one or both dates. Use format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS"
            
            diff = abs((dt2 - dt1).days)
            return f"Time difference between {date1} and {date2}: {diff} days"
            
        except Exception as e:
            return f"Error calculating time difference: {str(e)}"
    
    return [
        Tool(
            name="get_current_time",
            description="Get the current date and time",
            parameters={"type": "object", "properties": {}, "required": []},
            function=get_current_time
        ),
        Tool(
            name="set_reminder",
            description="Set a reminder for a specific time in the future",
            parameters={
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "The reminder message"
                    },
                    "minutes": {
                        "type": "integer",
                        "description": "Number of minutes from now to set the reminder"
                    }
                },
                "required": ["message", "minutes"]
            },
            function=set_reminder
        ),
        Tool(
            name="calculate_time_difference",
            description="Calculate the difference in days between two dates",
            parameters={
                "type": "object",
                "properties": {
                    "date1": {
                        "type": "string",
                        "description": "First date (YYYY-MM-DD format)"
                    },
                    "date2": {
                        "type": "string",
                        "description": "Second date (YYYY-MM-DD format)"
                    }
                },
                "required": ["date1", "date2"]
            },
            function=calculate_time_difference
        )
    ]

# =============================================================================
# FUN TOOLS
# =============================================================================

def create_fun_tools() -> List[Tool]:
    """Create entertaining tools for the agent"""
    
    def tell_joke() -> str:
        """Tell a random programming joke"""
        jokes = [
            "Why do programmers prefer dark mode? Because light attracts bugs! 🐛",
            "How many programmers does it take to change a light bulb? None, that's a hardware problem! 💡",
            "Why do Java developers wear glasses? Because they can't C# ! 👓",
            "What's a programmer's favorite hangout place? Foo Bar! 🍺",
            "Why did the programmer quit his job? He didn't get arrays! 📊",
            "What do you call a programmer from Finland? Nerdic! 🇫🇮",
            "Why do programmers hate nature? It has too many bugs! 🌿🐛",
            "What's the object-oriented way to become wealthy? Inheritance! 💰"
        ]
        return random.choice(jokes)
    
    def generate_password(length: int = 12) -> str:
        """Generate a random password"""
        import string
        
        if length < 4:
            return "Error: Password length must be at least 4 characters"
        if length > 50:
            return "Error: Password length cannot exceed 50 characters"
        
        # Ensure password has at least one of each type
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        password = (
            random.choice(string.ascii_lowercase) +
            random.choice(string.ascii_uppercase) +
            random.choice(string.digits) +
            random.choice("!@#$%^&*")
        )
        
        # Fill the rest randomly
        for _ in range(length - 4):
            password += random.choice(chars)
        
        # Shuffle the password
        password_list = list(password)
        random.shuffle(password_list)
        
        return f"Generated password: {''.join(password_list)}"
    
    def flip_coin() -> str:
        """Flip a coin"""
        result = random.choice(["Heads", "Tails"])
        return f"🪙 Coin flip result: {result}!"
    
    return [
        Tool(
            name="tell_joke",
            description="Tell a random programming joke to lighten the mood",
            parameters={"type": "object", "properties": {}, "required": []},
            function=tell_joke
        ),
        Tool(
            name="generate_password",
            description="Generate a secure random password",
            parameters={
                "type": "object",
                "properties": {
                    "length": {
                        "type": "integer",
                        "description": "Length of the password (4-50 characters, default: 12)"
                    }
                },
                "required": []
            },
            function=generate_password
        ),
        Tool(
            name="flip_coin",
            description="Flip a coin to make a random decision",
            parameters={"type": "object", "properties": {}, "required": []},
            function=flip_coin
        )
    ]

# =============================================================================
# TOOL COLLECTION FUNCTION
# =============================================================================

def get_all_tools() -> List[Tool]:
    """Get all available tools"""
    tools = []
    tools.append(create_calculator_tool())
    tools.append(create_weather_tool())
    tools.extend(create_file_tools())
    tools.extend(create_time_tools())
    tools.extend(create_fun_tools())
    return tools

# Example usage
if __name__ == "__main__":
    print("🔧 Available Tools:")
    tools = get_all_tools()
    for i, tool in enumerate(tools, 1):
        print(f"{i}. {tool.name}: {tool.description}")
    
    print(f"\nTotal tools available: {len(tools)}")
