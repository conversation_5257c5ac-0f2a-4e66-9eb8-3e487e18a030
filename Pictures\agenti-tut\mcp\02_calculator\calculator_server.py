#!/usr/bin/env python3
"""
🧮 Calculator MCP Server

A more advanced MCP server demonstrating:
- Multiple mathematical tools
- Input validation and error handling
- Different parameter types (strings, numbers, booleans)
- Complex tool interactions

This server provides mathematical capabilities to LLMs via MCP.
"""

import math
import re
from typing import List
from mcp.server.fastmcp import FastMCP

# Create MCP server instance
mcp = FastMCP("Calculator Server")

@mcp.tool()
async def calculate(expression: str) -> str:
    """
    Safely evaluate mathematical expressions.
    
    Args:
        expression: Mathematical expression (e.g., "2 + 3 * 4", "(10 + 5) / 3")
        
    Returns:
        The calculated result or error message
    """
    try:
        # Clean the expression
        expression = expression.strip()
        
        if not expression:
            return "❌ Error: Please provide a mathematical expression"
        
        # Only allow safe mathematical operations
        allowed_chars = set('0123456789+-*/.() ')
        if not all(c in allowed_chars for c in expression):
            return "❌ Error: Only basic math operations are allowed (+, -, *, /, parentheses, numbers)"
        
        # Check for dangerous patterns
        dangerous_patterns = ['__', 'import', 'exec', 'eval', 'open', 'file']
        if any(pattern in expression.lower() for pattern in dangerous_patterns):
            return "❌ Error: Invalid expression detected"
        
        # Evaluate safely
        result = eval(expression)
        
        # Format the result nicely
        if isinstance(result, float):
            if result.is_integer():
                result = int(result)
            else:
                result = round(result, 10)  # Avoid floating point precision issues
        
        return f"🧮 {expression} = {result}"
        
    except ZeroDivisionError:
        return "❌ Error: Cannot divide by zero"
    except SyntaxError:
        return "❌ Error: Invalid mathematical expression"
    except Exception as e:
        return f"❌ Error: {str(e)}"

@mcp.tool()
async def power(base: float, exponent: float) -> str:
    """
    Calculate base raised to exponent power.
    
    Args:
        base: The base number
        exponent: The exponent (can be fractional for roots)
        
    Returns:
        The power calculation result
    """
    try:
        if base == 0 and exponent < 0:
            return "❌ Error: Cannot raise 0 to a negative power"
        
        if base < 0 and not float(exponent).is_integer():
            return "❌ Error: Cannot calculate fractional power of negative number"
        
        result = base ** exponent
        
        # Handle very large or very small numbers
        if abs(result) > 1e15:
            return f"🔢 {base}^{exponent} = {result:.2e} (scientific notation)"
        elif abs(result) < 1e-10 and result != 0:
            return f"🔢 {base}^{exponent} = {result:.2e} (scientific notation)"
        elif isinstance(result, float) and result.is_integer():
            return f"🔢 {base}^{exponent} = {int(result)}"
        else:
            return f"🔢 {base}^{exponent} = {round(result, 10)}"
            
    except OverflowError:
        return "❌ Error: Result is too large to calculate"
    except Exception as e:
        return f"❌ Error: {str(e)}"

@mcp.tool()
async def factorial(n: int) -> str:
    """
    Calculate the factorial of a number (n!).
    
    Args:
        n: Non-negative integer to calculate factorial for
        
    Returns:
        The factorial result
    """
    try:
        if n < 0:
            return "❌ Error: Factorial is not defined for negative numbers"
        
        if n > 170:  # Prevent overflow
            return "❌ Error: Number too large for factorial calculation (max: 170)"
        
        result = math.factorial(n)
        return f"🔢 {n}! = {result}"
        
    except Exception as e:
        return f"❌ Error: {str(e)}"

@mcp.tool()
async def statistics(numbers: str) -> str:
    """
    Calculate basic statistics for a list of numbers.
    
    Args:
        numbers: Comma-separated list of numbers (e.g., "1, 2, 3, 4, 5")
        
    Returns:
        Statistical summary including mean, median, mode, range
    """
    try:
        # Parse the numbers
        number_list = []
        for num_str in numbers.split(','):
            num_str = num_str.strip()
            if num_str:
                number_list.append(float(num_str))
        
        if not number_list:
            return "❌ Error: Please provide at least one number"
        
        if len(number_list) > 1000:
            return "❌ Error: Too many numbers (max: 1000)"
        
        # Calculate statistics
        mean = sum(number_list) / len(number_list)
        sorted_nums = sorted(number_list)
        
        # Median
        n = len(sorted_nums)
        if n % 2 == 0:
            median = (sorted_nums[n//2 - 1] + sorted_nums[n//2]) / 2
        else:
            median = sorted_nums[n//2]
        
        # Range
        range_val = max(number_list) - min(number_list)
        
        # Mode (most frequent)
        from collections import Counter
        counts = Counter(number_list)
        max_count = max(counts.values())
        modes = [num for num, count in counts.items() if count == max_count]
        
        if len(modes) == len(number_list):
            mode_str = "No mode (all values unique)"
        elif len(modes) == 1:
            mode_str = f"{modes[0]}"
        else:
            mode_str = f"Multiple modes: {modes[:3]}{'...' if len(modes) > 3 else ''}"
        
        # Standard deviation
        variance = sum((x - mean) ** 2 for x in number_list) / len(number_list)
        std_dev = math.sqrt(variance)
        
        result = f"""📊 Statistics for {len(number_list)} numbers:
• Mean (average): {round(mean, 4)}
• Median (middle): {round(median, 4)}
• Mode (most frequent): {mode_str}
• Range (max - min): {round(range_val, 4)}
• Standard deviation: {round(std_dev, 4)}
• Min: {min(number_list)}
• Max: {max(number_list)}
• Sum: {sum(number_list)}"""
        
        return result
        
    except ValueError:
        return "❌ Error: Please provide valid numbers separated by commas"
    except Exception as e:
        return f"❌ Error: {str(e)}"

@mcp.tool()
async def convert_base(number: str, from_base: int, to_base: int) -> str:
    """
    Convert a number from one base to another.
    
    Args:
        number: The number to convert (as string)
        from_base: Source base (2-36)
        to_base: Target base (2-36)
        
    Returns:
        The converted number
    """
    try:
        if from_base < 2 or from_base > 36:
            return "❌ Error: Source base must be between 2 and 36"
        
        if to_base < 2 or to_base > 36:
            return "❌ Error: Target base must be between 2 and 36"
        
        # Convert from source base to decimal
        decimal_value = int(number, from_base)
        
        if decimal_value > 10**10:
            return "❌ Error: Number too large for conversion"
        
        # Convert from decimal to target base
        if to_base == 10:
            result = str(decimal_value)
        else:
            digits = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
            if decimal_value == 0:
                result = "0"
            else:
                result = ""
                temp = abs(decimal_value)
                while temp > 0:
                    result = digits[temp % to_base] + result
                    temp //= to_base
                if decimal_value < 0:
                    result = "-" + result
        
        return f"🔢 {number} (base {from_base}) = {result} (base {to_base})"
        
    except ValueError:
        return f"❌ Error: '{number}' is not a valid number in base {from_base}"
    except Exception as e:
        return f"❌ Error: {str(e)}"

@mcp.tool()
async def get_calculator_help() -> str:
    """
    Get help information about the calculator server.
    
    Returns:
        Help information and available tools
    """
    return """🧮 Calculator MCP Server Help

Available Mathematical Tools:

🔢 Basic Operations:
• calculate - Evaluate expressions like "2 + 3 * 4" or "(10 + 5) / 3"
• power - Calculate base^exponent (supports fractional exponents for roots)
• factorial - Calculate n! for non-negative integers

📊 Statistics:
• statistics - Calculate mean, median, mode, range, std dev for number lists

🔄 Number Systems:
• convert_base - Convert numbers between different bases (2-36)

💡 Tips:
• Use parentheses for complex expressions: "(2 + 3) * 4"
• For square root: power(9, 0.5) or power(9, 1/2)
• For cube root: power(27, 1/3)
• Statistics format: "1, 2, 3, 4, 5"
• Binary conversion: convert_base("1010", 2, 10)

⚠️ Safety Features:
• Input validation and sanitization
• Error handling for edge cases
• Protection against dangerous operations
• Reasonable limits on computation size

Built with: MCP Python SDK
Compatible with: Claude Desktop and other MCP clients
"""

def main():
    """Run the Calculator MCP server"""
    print("🧮 Starting Calculator MCP Server...")
    print("Available tools: calculate, power, factorial, statistics, convert_base, get_calculator_help")
    print("Server is ready for MCP connections.")
    print("Use Ctrl+C to stop the server.")
    
    # Run the server with stdio transport
    mcp.run(transport="stdio")

if __name__ == "__main__":
    main()
