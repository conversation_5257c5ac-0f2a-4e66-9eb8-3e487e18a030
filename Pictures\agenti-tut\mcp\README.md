# 🔗 Model Context Protocol (MCP) Examples

Welcome to the **real** Model Context Protocol section! MCP is an open protocol that standardizes how applications provide context to LLMs.

## 🎯 What is MCP Really?

MCP is a **client-server protocol** that enables:
- **MCP Servers**: Expose tools, resources, and prompts to LLMs
- **MCP Clients**: Connect to servers and make capabilities available to LLMs  
- **Standardized Communication**: JSON-RPC over stdio, SSE, or WebSocket

Think of MCP like USB-C for AI - a standardized way to connect AI models to data sources and tools.

## 🏗️ MCP Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LLM Client    │◄──►│   MCP Client    │◄──►│   MCP Server    │
│  (Claude, etc)  │    │  (Protocol)     │    │   (Your Code)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │  Data Sources   │
                                               │ APIs, Files,    │
                                               │ Databases, etc  │
                                               └─────────────────┘
```

## 📁 Workshop Structure

```
mcp/
├── 📖 README.md                    # This overview
├── 🌟 01_hello_world/              # Simplest MCP server
│   ├── hello_server.py             # Basic MCP server with greeting tool
│   ├── test_client.py              # Test client to verify server
│   └── README.md                   # Step-by-step guide
├── 🧮 02_calculator/               # Math tools MCP server
│   ├── calculator_server.py        # Calculator and math operations
│   ├── test_calculator.py          # Test the calculator server
│   └── README.md                   # Calculator server guide
├── 🌐 03_weather_api/              # External API integration
│   ├── weather_server.py           # Real weather API server
│   ├── test_weather.py             # Test weather server
│   └── README.md                   # Weather API guide
├── 🎮 04_fun_demo/                 # Interactive fun server
│   ├── game_server.py              # Text adventure game
│   ├── story_server.py             # Story generator
│   └── README.md                   # Fun demo guide
├── 🔧 05_advanced/                 # Advanced MCP patterns
│   ├── multi_tool_server.py        # Server with multiple tools
│   ├── resource_server.py          # Server with resources
│   ├── prompt_server.py            # Server with prompts
│   └── README.md                   # Advanced patterns
└── 📋 requirements.txt             # MCP dependencies
```

## 🚀 Quick Start

### 1. Install MCP SDK
```bash
cd mcp
pip install -r requirements.txt
```

### 2. Run Hello World Server
```bash
cd 01_hello_world
python hello_server.py
```

### 3. Test with Client
```bash
# In another terminal
python test_client.py
```

### 4. Connect to Claude Desktop
Add to `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "hello": {
      "command": "python",
      "args": ["/absolute/path/to/hello_server.py"]
    }
  }
}
```

## 🎓 Learning Path

### 🌟 Beginner (Start Here!)
1. **Hello World** (`01_hello_world/`) - Basic MCP server with one tool
2. **Calculator** (`02_calculator/`) - Multiple tools, input validation

### 🔧 Intermediate  
3. **Weather API** (`03_weather_api/`) - External API integration
4. **Fun Demo** (`04_fun_demo/`) - Interactive examples

### 🚀 Advanced
5. **Advanced Patterns** (`05_advanced/`) - Resources, prompts, complex workflows

## 🔧 Key MCP Concepts

### 1. **Tools** - Functions LLMs can call
```python
@server.tool()
async def greet(name: str) -> str:
    """Greet someone by name"""
    return f"Hello {name}!"
```

### 2. **Resources** - Data LLMs can read
```python
@server.resource("file://{path}")
async def read_file(path: str) -> str:
    """Read file contents"""
    with open(path) as f:
        return f.read()
```

### 3. **Prompts** - Reusable templates
```python
@server.prompt()
async def code_review() -> str:
    """Code review prompt template"""
    return "Please review this code for..."
```

## 🌟 What Makes This Different

### ❌ What I Built Before (Wrong!)
- Custom tool calling format
- Direct function execution
- No standardized protocol

### ✅ Real MCP (Correct!)
- Official MCP Python SDK
- JSON-RPC protocol communication
- Compatible with Claude Desktop
- Standardized tool/resource/prompt format
- Client-server architecture

## 🎯 Workshop Integration

These MCP examples work with:
- **Claude Desktop** - Official MCP client
- **Custom MCP Clients** - Build your own
- **Other MCP Hosts** - Any MCP-compatible application

## 📚 Official Resources

- [MCP Specification](https://modelcontextprotocol.io/specification)
- [Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [Example Servers](https://modelcontextprotocol.io/examples)

---

**Ready to build real MCP servers? Start with `01_hello_world/`! 🚀**
