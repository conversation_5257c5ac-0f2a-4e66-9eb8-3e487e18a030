"""
🤖 Personal Assistant Agent - Your AI Coding Buddy!

This is a fun, interactive personal assistant that demonstrates
the power of agentic AI. It can help with coding tasks, manage files,
tell jokes, and much more!
"""

from agent_core import MistralAgent
from tools import get_all_tools
import os

class PersonalAssistant(MistralAgent):
    """
    A specialized personal assistant agent with enhanced capabilities
    """
    
    def __init__(self):
        # Create a friendly system prompt
        system_prompt = """You are <PERSON>, a friendly and helpful AI personal assistant for developers.

Your personality:
- Enthusiastic about helping with coding and development tasks
- Patient and encouraging, especially with junior developers
- Use emojis occasionally to be more engaging
- Explain technical concepts in simple terms
- Always try to be helpful and positive

Your capabilities:
- Help with coding questions and problems
- Manage files and directories
- Perform calculations and data analysis
- Provide weather information
- Tell programming jokes to lighten the mood
- Generate secure passwords
- Help with time management and reminders
- And much more through your tools!

When using tools:
- Explain what you're doing and why
- Show the results clearly
- Offer follow-up suggestions when appropriate

Remember: You're here to make development more fun and productive! 🚀"""

        super().__init__(name="<PERSON>", system_prompt=system_prompt)
        
        # Add all available tools
        self._setup_tools()
        
        # Welcome message
        print("🤖 Alex: Hi there! I'm <PERSON>, your personal AI assistant!")
        print("I'm here to help you with coding, file management, and much more!")
        print(f"I have {len(self.tools)} tools at my disposal. Try asking me to:")
        print("  • Calculate something: 'What's 15 * 23 + 7?'")
        print("  • Check weather: 'What's the weather in Tokyo?'")
        print("  • Manage files: 'List the files in this directory'")
        print("  • Tell a joke: 'Tell me a programming joke'")
        print("  • Generate password: 'Create a secure password'")
        print("  • Or anything else you can think of!")
        print("\nType 'help' for more commands or 'quit' to exit.\n")
    
    def _setup_tools(self):
        """Set up all tools for the assistant"""
        tools = get_all_tools()
        for tool in tools:
            self.add_tool(tool)
    
    def run_interactive(self):
        """Run the assistant in interactive mode"""
        print("=" * 60)
        print("🚀 Personal Assistant Interactive Mode")
        print("=" * 60)
        
        while True:
            try:
                # Get user input
                user_input = input("\n👤 You: ").strip()
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("🤖 Alex: Goodbye! Happy coding! 👋")
                    break
                
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                
                elif user_input.lower() == 'tools':
                    self._show_tools()
                    continue
                
                elif user_input.lower() == 'clear':
                    self.clear_history()
                    continue
                
                elif user_input.lower() == 'summary':
                    print(self.get_conversation_summary())
                    continue
                
                elif not user_input:
                    print("🤖 Alex: I'm here when you need me! Just ask me anything.")
                    continue
                
                # Process the message with the agent
                response = self.chat(user_input)
                
            except KeyboardInterrupt:
                print("\n🤖 Alex: Goodbye! Happy coding! 👋")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                print("🤖 Alex: Sorry, something went wrong. Please try again!")
    
    def _show_help(self):
        """Show help information"""
        help_text = """
🆘 Alex's Help Menu:

Special Commands:
  • help     - Show this help menu
  • tools    - List all available tools
  • clear    - Clear conversation history
  • summary  - Show conversation summary
  • quit     - Exit the assistant

Example Requests:
  • "Calculate 15 * 23 + 7"
  • "What's the weather in London?"
  • "List files in this directory"
  • "Create a file called test.txt with some content"
  • "Tell me a programming joke"
  • "Generate a 16-character password"
  • "What time is it?"
  • "Set a reminder to take a break in 30 minutes"
  • "Flip a coin to decide"

Tips:
  • Be specific about what you want
  • I can combine multiple tasks in one request
  • Ask follow-up questions anytime
  • I'm here to help you learn and be productive!
"""
        print(help_text)
    
    def _show_tools(self):
        """Show all available tools"""
        print("\n🔧 Alex's Toolkit:")
        print("-" * 40)
        
        categories = {
            "Math & Calculation": ["calculate"],
            "Weather & Info": ["get_weather"],
            "File Management": ["read_file", "write_file", "list_files"],
            "Time & Scheduling": ["get_current_time", "set_reminder", "calculate_time_difference"],
            "Fun & Utilities": ["tell_joke", "generate_password", "flip_coin"]
        }
        
        for category, tool_names in categories.items():
            print(f"\n📂 {category}:")
            for tool_name in tool_names:
                if tool_name in self.tools:
                    tool = self.tools[tool_name]
                    print(f"  • {tool.name}: {tool.description}")
        
        print(f"\nTotal: {len(self.tools)} tools available")

def demo_assistant():
    """Run a quick demo of the assistant"""
    print("🎬 Personal Assistant Demo")
    print("=" * 40)
    
    # Create assistant
    assistant = PersonalAssistant()
    
    # Demo conversations
    demo_requests = [
        "What's 25 * 4 + 10?",
        "What's the weather like in Paris?",
        "Tell me a programming joke",
        "What time is it?",
        "Generate a secure password"
    ]
    
    print("\n🎭 Demo Conversation:")
    for request in demo_requests:
        print(f"\n👤 Demo User: {request}")
        response = assistant.chat(request)
        print("-" * 50)
    
    print("\n✅ Demo completed! The assistant is ready for real use.")
    return assistant

# Main execution
if __name__ == "__main__":
    print("🚀 Starting Personal Assistant...")
    
    # Check if API key is available
    if not os.getenv("MISTRAL_API_KEY"):
        print("❌ Error: MISTRAL_API_KEY not found in environment variables")
        print("Please add your Mistral API key to the .env file")
        print("\nFor demo purposes, you can still see the code structure!")
        exit(1)
    
    try:
        # Ask user what they want to do
        print("\nWhat would you like to do?")
        print("1. Run interactive assistant")
        print("2. Run quick demo")
        print("3. Just create assistant (for testing)")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            assistant = PersonalAssistant()
            assistant.run_interactive()
        
        elif choice == "2":
            demo_assistant()
        
        elif choice == "3":
            assistant = PersonalAssistant()
            print("✅ Assistant created successfully!")
            print("You can now use assistant.chat('your message') to interact with it.")
        
        else:
            print("Invalid choice. Creating assistant for manual testing...")
            assistant = PersonalAssistant()
    
    except Exception as e:
        print(f"❌ Error starting assistant: {str(e)}")
        print("Make sure you have:")
        print("1. Added MISTRAL_API_KEY to your .env file")
        print("2. Installed required packages: pip install openai python-dotenv")
