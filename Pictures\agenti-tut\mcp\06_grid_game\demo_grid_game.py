#!/usr/bin/env python3
"""
🤖 Grid Bot Adventure Demo

Test the grid game logic without MCP to verify everything works correctly.
This demonstrates the strategic gameplay that LLMs will control through MCP.
"""

import math
import random
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass, field

# Game constants
GRID_WIDTH = 10
GRID_HEIGHT = 10
EMPTY = "⬜"
WALL = "⬛"
BOT = "🤖"
TURTLE_GREEN = "🐢"
TURTLE_RED = "🔴"
TURTLE_BLUE = "🔵"
COIN = "🪙"
GOLDEN_COIN = "🏆"

@dataclass
class Position:
    x: int
    y: int
    
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y
    
    def distance_to(self, other):
        return abs(self.x - other.x) + abs(self.y - other.y)

@dataclass
class Bot:
    position: Position
    alive: bool = True
    moves_made: int = 0

@dataclass
class Turtle:
    position: Position
    turtle_type: str  # "green", "red", "blue"
    direction: str = "north"
    chase_range: int = 3
    
    def get_emoji(self):
        return {"green": TURTLE_GREEN, "red": TURTLE_RED, "blue": TURTLE_BLUE}[self.turtle_type]

@dataclass
class Coin:
    position: Position
    value: int = 10
    is_golden: bool = False
    
    def get_emoji(self):
        return GOLDEN_COIN if self.is_golden else COIN

@dataclass
class GameState:
    grid: List[List[str]] = field(default_factory=list)
    bot: Optional[Bot] = None
    turtles: List[Turtle] = field(default_factory=list)
    coins: List[Coin] = field(default_factory=list)
    score: int = 0
    level: int = 1
    game_over: bool = False
    victory: bool = False
    turn_count: int = 0

# Global game state
game_state = None

def create_empty_grid():
    """Create an empty grid with walls around the border"""
    grid = []
    for y in range(GRID_HEIGHT):
        row = []
        for x in range(GRID_WIDTH):
            if x == 0 or x == GRID_WIDTH-1 or y == 0 or y == GRID_HEIGHT-1:
                row.append(WALL)
            else:
                row.append(EMPTY)
        grid.append(row)
    return grid

def add_random_walls(grid, count=6):
    """Add some random walls for obstacles"""
    for _ in range(count):
        while True:
            x = random.randint(2, GRID_WIDTH-3)
            y = random.randint(2, GRID_HEIGHT-3)
            if grid[y][x] == EMPTY:
                grid[y][x] = WALL
                break

def is_valid_position(x, y):
    """Check if position is within grid bounds and not a wall"""
    if not (0 <= x < GRID_WIDTH and 0 <= y < GRID_HEIGHT):
        return False
    return game_state.grid[y][x] not in [WALL]

def get_adjacent_positions(pos):
    """Get valid adjacent positions"""
    directions = [(0, -1), (0, 1), (-1, 0), (1, 0)]  # north, south, west, east
    adjacent = []
    for dx, dy in directions:
        new_x, new_y = pos.x + dx, pos.y + dy
        if is_valid_position(new_x, new_y):
            adjacent.append(Position(new_x, new_y))
    return adjacent

def move_turtle(turtle):
    """Move a turtle based on its type and behavior"""
    if turtle.turtle_type == "green":
        # Random movement
        adjacent = get_adjacent_positions(turtle.position)
        if adjacent:
            turtle.position = random.choice(adjacent)
    
    elif turtle.turtle_type == "red":
        # Chase player if within range
        bot_pos = game_state.bot.position
        distance = turtle.position.distance_to(bot_pos)
        
        if distance <= turtle.chase_range:
            # Move toward player
            dx = bot_pos.x - turtle.position.x
            dy = bot_pos.y - turtle.position.y
            
            # Choose best direction
            if abs(dx) > abs(dy):
                new_x = turtle.position.x + (1 if dx > 0 else -1)
                new_y = turtle.position.y
            else:
                new_x = turtle.position.x
                new_y = turtle.position.y + (1 if dy > 0 else -1)
            
            if is_valid_position(new_x, new_y):
                turtle.position = Position(new_x, new_y)
        else:
            # Random movement when not chasing
            adjacent = get_adjacent_positions(turtle.position)
            if adjacent:
                turtle.position = random.choice(adjacent)
    
    elif turtle.turtle_type == "blue":
        # Patrol pattern
        directions = {"north": (0, -1), "south": (0, 1), "west": (-1, 0), "east": (1, 0)}
        dx, dy = directions[turtle.direction]
        new_x = turtle.position.x + dx
        new_y = turtle.position.y + dy
        
        if is_valid_position(new_x, new_y):
            turtle.position = Position(new_x, new_y)
        else:
            # Change direction when hitting obstacle
            turtle.direction = random.choice(["north", "south", "west", "east"])

def update_grid_display():
    """Update the visual grid with current positions"""
    # Reset grid to empty/walls
    for y in range(GRID_HEIGHT):
        for x in range(GRID_WIDTH):
            if game_state.grid[y][x] not in [WALL]:
                game_state.grid[y][x] = EMPTY
    
    # Place coins
    for coin in game_state.coins:
        game_state.grid[coin.position.y][coin.position.x] = coin.get_emoji()
    
    # Place turtles
    for turtle in game_state.turtles:
        game_state.grid[turtle.position.y][turtle.position.x] = turtle.get_emoji()
    
    # Place bot (on top of everything)
    if game_state.bot and game_state.bot.alive:
        game_state.grid[game_state.bot.position.y][game_state.bot.position.x] = BOT

def initialize_game():
    """Initialize a new game"""
    global game_state
    
    # Create grid
    grid = create_empty_grid()
    add_random_walls(grid, 6)
    
    # Create bot at safe position
    bot = Bot(Position(2, 2))
    
    # Create turtles
    turtles = [
        Turtle(Position(7, 7), "green"),
        Turtle(Position(3, 8), "red"),
        Turtle(Position(8, 3), "blue")
    ]
    
    # Create coins
    coins = [
        Coin(Position(4, 4), 10, False),
        Coin(Position(6, 2), 50, True),  # Golden coin
        Coin(Position(2, 6), 10, False),
        Coin(Position(8, 8), 10, False),
        Coin(Position(5, 7), 10, False),
        Coin(Position(7, 4), 50, True),  # Golden coin
        Coin(Position(3, 3), 10, False),
        Coin(Position(6, 6), 10, False),
    ]
    
    game_state = GameState(
        grid=grid,
        bot=bot,
        turtles=turtles,
        coins=coins,
        score=0,
        level=1,
        game_over=False,
        victory=False,
        turn_count=0
    )
    
    update_grid_display()

def format_grid():
    """Format the grid for display"""
    result = "🗺️ Game Grid:\n"
    result += "  " + "".join(f"{i}" for i in range(GRID_WIDTH)) + "\n"
    
    for y, row in enumerate(game_state.grid):
        result += f"{y} " + "".join(row) + "\n"
    
    return result

def start_new_game():
    """Start a new grid bot adventure game."""
    initialize_game()
    
    return f"""🤖 Grid Bot Adventure Started!

🎯 Mission: Collect all coins while avoiding turtle enemies!

📊 Game Setup:
• Grid Size: {GRID_WIDTH}x{GRID_HEIGHT}
• Bot Position: ({game_state.bot.position.x}, {game_state.bot.position.y})
• Turtles: {len(game_state.turtles)} enemies
• Coins: {len(game_state.coins)} to collect
• Score: {game_state.score}

🐢 Enemy Types:
• {TURTLE_GREEN} Green: Moves randomly
• {TURTLE_RED} Red: Chases when nearby
• {TURTLE_BLUE} Blue: Patrols in patterns

{format_grid()}

Ready for adventure! 🚀"""

def move_bot(direction: str):
    """Move the bot in the specified direction."""
    if not game_state:
        return "❌ No game in progress. Start a new game first!"
    
    if game_state.game_over:
        return "❌ Game is over! Start a new game to play again."
    
    if not game_state.bot.alive:
        return "❌ Bot is not alive! Game over."
    
    # Parse direction
    direction = direction.lower().strip()
    direction_map = {
        "north": (0, -1), "up": (0, -1),
        "south": (0, 1), "down": (0, 1),
        "east": (1, 0), "right": (1, 0),
        "west": (-1, 0), "left": (-1, 0)
    }
    
    if direction not in direction_map:
        return f"❌ Invalid direction '{direction}'. Use: north, south, east, west"
    
    dx, dy = direction_map[direction]
    new_x = game_state.bot.position.x + dx
    new_y = game_state.bot.position.y + dy
    
    # Check if move is valid
    if not is_valid_position(new_x, new_y):
        return f"❌ Cannot move {direction} - blocked by wall or out of bounds!"
    
    # Move bot
    old_pos = game_state.bot.position
    game_state.bot.position = Position(new_x, new_y)
    game_state.bot.moves_made += 1
    game_state.turn_count += 1
    
    # Check for coin collection
    coins_collected = []
    for coin in game_state.coins[:]:
        if coin.position == game_state.bot.position:
            coins_collected.append(coin)
            game_state.coins.remove(coin)
            game_state.score += coin.value
    
    # Move turtles
    for turtle in game_state.turtles:
        move_turtle(turtle)
    
    # Check for collisions with turtles
    for turtle in game_state.turtles:
        if turtle.position == game_state.bot.position:
            game_state.bot.alive = False
            game_state.game_over = True
            update_grid_display()
            return f"""💀 GAME OVER!

🤖 Bot moved {direction} from ({old_pos.x}, {old_pos.y}) to ({new_x}, {new_y})
🐢 Caught by {turtle.get_emoji()} {turtle.turtle_type} turtle!

📊 Final Stats:
• Score: {game_state.score} points
• Moves: {game_state.bot.moves_made}
• Coins Collected: {8 - len(game_state.coins)}/8"""
    
    # Check victory condition
    if len(game_state.coins) == 0:
        game_state.victory = True
        game_state.game_over = True
        update_grid_display()
        return f"""🎉 VICTORY!

🤖 Bot moved {direction} and collected the final coin!
🏆 All coins collected successfully!

📊 Final Stats:
• Score: {game_state.score} points
• Moves: {game_state.bot.moves_made}
• Efficiency: {game_state.score / game_state.bot.moves_made:.1f} points per move"""
    
    # Update grid display
    update_grid_display()
    
    # Format result
    result = f"""🤖 Bot moved {direction}!

📍 Position: ({old_pos.x}, {old_pos.y}) → ({new_x}, {new_y})
"""
    
    if coins_collected:
        for coin in coins_collected:
            result += f"🪙 Collected {coin.get_emoji()} worth {coin.value} points!\n"
        result += f"💰 Score: {game_state.score} points\n"
    
    result += f"🎯 Coins remaining: {len(game_state.coins)}\n"
    result += f"🐢 Turtles moved to new positions\n"
    
    return result

def get_game_state():
    """Get the current game state and grid."""
    if not game_state:
        return "❌ No game in progress."
    
    status = f"""📊 Current Game State:

🤖 Bot Status:
• Position: ({game_state.bot.position.x}, {game_state.bot.position.y})
• Alive: {'✅' if game_state.bot.alive else '💀'}
• Moves Made: {game_state.bot.moves_made}

🎯 Objectives:
• Score: {game_state.score} points
• Coins Remaining: {len(game_state.coins)}
• Turn: {game_state.turn_count}

{format_grid()}"""
    
    if game_state.game_over:
        if game_state.victory:
            status += "\n🎉 VICTORY! All coins collected!"
        else:
            status += "\n💀 GAME OVER! Bot was caught by a turtle!"
    
    return status

def demo_ai_gameplay():
    """Simulate how an AI would play the game"""
    print("🤖 AI Grid Bot Adventure Demo")
    print("=" * 50)
    print("Simulating how an LLM would strategically play the grid game!")
    print()
    
    # Start game
    print("🎯 STEP 1: Starting new game...")
    result = start_new_game()
    print(result)
    print()
    
    # AI makes strategic moves
    moves = [
        ("east", "Moving east to explore"),
        ("north", "Moving north to avoid potential threats"),
        ("east", "Continuing east toward coins"),
        ("south", "Moving south to collect coin"),
        ("west", "Moving west for strategic positioning"),
        ("north", "Moving north to collect golden coin"),
        ("south", "Moving south to continue collection"),
        ("east", "Moving east for more coins")
    ]
    
    for i, (direction, strategy) in enumerate(moves, 2):
        if game_state and not game_state.game_over:
            print(f"🚀 STEP {i}: {strategy}")
            print(f"   Moving {direction}...")
            result = move_bot(direction)
            print(result)
            print()
            
            if game_state.game_over:
                break
    
    print("=" * 50)
    print("🎉 AI Gameplay Demo Complete!")
    print("=" * 50)
    print("\nThis demonstrates:")
    print("✅ Strategic grid navigation")
    print("✅ Coin collection mechanics")
    print("✅ Enemy avoidance behavior")
    print("✅ Turn-based gameplay")
    print("✅ Emoji-based visual representation")
    print("\n🔗 This same logic powers the MCP server that LLMs can control!")

def interactive_demo():
    """Interactive demo where user can play"""
    print("🎮 Interactive Grid Bot Adventure Demo")
    print("=" * 45)
    print("Play the game manually to test the mechanics!")
    print()
    
    # Start game
    result = start_new_game()
    print(result)
    print()
    
    while game_state and not game_state.game_over:
        print("\n" + "-" * 40)
        print("🎮 Your turn! Enter movement direction:")
        print("Commands: north, south, east, west, state, quit")
        
        try:
            command = input("Enter command: ").strip().lower()
            
            if command == "quit":
                print("👋 Thanks for playing!")
                break
            elif command == "state":
                print(get_game_state())
            elif command in ["north", "south", "east", "west"]:
                print(f"\n🚀 Moving {command}...")
                result = move_bot(command)
                print(result)
            else:
                print("❌ Invalid command! Use: north, south, east, west, state, quit")
            
        except KeyboardInterrupt:
            print("\n\n👋 Thanks for playing!")
            break
    
    if game_state and game_state.game_over:
        print("\n🎮 Game Over! Thanks for playing the grid bot demo!")

def main():
    """Run the demo"""
    print("🤖 Grid Bot Adventure Demo - Choose Your Adventure!")
    print("=" * 55)
    print("1. 🤖 AI Gameplay Simulation")
    print("2. 🎮 Interactive Demo (You Play)")
    print("3. 🧪 Quick Test")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            demo_ai_gameplay()
        elif choice == "2":
            interactive_demo()
        elif choice == "3":
            print("\n🧪 Quick Test:")
            print(start_new_game())
            print("\n" + move_bot("east"))
            print("\n" + get_game_state())
        else:
            print("Invalid choice. Running AI demo...")
            demo_ai_gameplay()
            
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted. Goodbye!")

if __name__ == "__main__":
    main()
